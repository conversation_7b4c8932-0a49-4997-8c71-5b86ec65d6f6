<?xml version="1.0" encoding="UTF-8" ?>
<!--
 Simplified Solr Configuration for Bleen Agent
 Basic document retrieval without clustering
-->
<config>
  
  <!-- Lucene Version -->
  <luceneMatchVersion>9.4.2</luceneMatchVersion>
  
  <!-- Data Directory -->
  <dataDir>${solr.data.dir:}</dataDir>
  
  <!-- Index Configuration -->
  <indexConfig>
    <lockType>${solr.lock.type:native}</lockType>
    <infoStream>true</infoStream>
  </indexConfig>
  
  <!-- Update Handler -->
  <updateHandler class="solr.DirectUpdateHandler2">
    <updateLog>
      <str name="dir">${solr.ulog.dir:}</str>
      <int name="numVersionBuckets">${solr.ulog.numVersionBuckets:65536}</int>
    </updateLog>
    <autoCommit>
      <maxTime>${solr.autoCommit.maxTime:15000}</maxTime>
      <openSearcher>false</openSearcher>
    </autoCommit>
    <autoSoftCommit>
      <maxTime>${solr.autoSoftCommit.maxTime:1000}</maxTime>
    </autoSoftCommit>
  </updateHandler>
  
  <!-- Query Configuration -->
  <query>
    <maxBooleanClauses>${solr.max.booleanClauses:1024}</maxBooleanClauses>
    <filterCache class="solr.FastLRUCache"
                 size="512"
                 initialSize="512"
                 autowarmCount="0"/>
    <queryResultCache class="solr.LRUCache"
                      size="512"
                      initialSize="512"
                      autowarmCount="0"/>
    <documentCache class="solr.LRUCache"
                   size="512"
                   initialSize="512"
                   autowarmCount="0"/>
    <cache name="perSegFilter"
           class="solr.search.LRUCache"
           size="10"
           initialSize="0"
           autowarmCount="10"
           regenerator="solr.NoOpRegenerator" />
    <enableLazyFieldLoading>true</enableLazyFieldLoading>
    <useFilterForSortedQuery>true</useFilterForSortedQuery>
    <queryResultWindowSize>20</queryResultWindowSize>
    <queryResultMaxDocsCached>200</queryResultMaxDocsCached>
    <listener event="newSearcher" class="solr.QuerySenderListener">
      <arr name="queries">
        <lst><str name="q">*:*</str></lst>
      </arr>
    </listener>
    <listener event="firstSearcher" class="solr.QuerySenderListener">
      <arr name="queries">
        <lst><str name="q">*:*</str></lst>
      </arr>
    </listener>
    <useColdSearcher>false</useColdSearcher>
  </query>
  
  <!-- Request Handlers -->
  
  <!-- Standard Request Handler -->
  <requestHandler name="/select" class="solr.SearchHandler">
    <lst name="defaults">
      <str name="echoParams">explicit</str>
      <int name="rows">10</int>
      <str name="df">content</str>
    </lst>
  </requestHandler>
  
  <!-- Update Request Handler -->
  <requestHandler name="/update" class="solr.UpdateRequestHandler">
  </requestHandler>
  
  <!-- More Like This Handler -->
  <requestHandler name="/mlt" class="solr.MoreLikeThisHandler">
    <lst name="defaults">
      <str name="mlt.fl">content,title</str>
      <int name="mlt.mindf">1</int>
      <int name="mlt.mintf">1</int>
      <int name="rows">5</int>
    </lst>
  </requestHandler>
  
  <!-- Admin Handlers -->
  <requestHandler name="/admin/ping" class="solr.PingRequestHandler">
    <lst name="invariants">
      <str name="q">*:*</str>
    </lst>
    <lst name="defaults">
      <str name="echoParams">all</str>
    </lst>
  </requestHandler>
  
</config>
