#!/usr/bin/env python3
"""
Script to import sample documents into Solr using DocumentImportTool directly
"""
import os
from pathlib import Path

def import_documents():
    print("🚀 Starting document import using DocumentImportTool...")

    # Path to sample documents
    docs_path = "/Users/<USER>/Documents/augment-projects/bleen-agent/examples/sample_documents/"

    # Check if path exists
    if not os.path.exists(docs_path):
        print(f"❌ Path not found: {docs_path}")
        return

    # List files in the directory
    print(f"📁 Checking directory: {docs_path}")
    files = list(Path(docs_path).rglob("*"))
    doc_files = [f for f in files if f.is_file() and f.suffix in ['.md', '.txt', '.pdf']]

    print(f"📄 Found {len(doc_files)} document files:")
    for file in doc_files:
        print(f"  - {file.name}")

    if not doc_files:
        print("❌ No documents found to import")
        return

    try:
        # Import the document import tool directly
        from bleen_agent.tools.document_management import DocumentImportTool

        print("🔧 Creating DocumentImportTool...")
        import_tool = DocumentImportTool()

        print("📥 Starting import process...")
        result = import_tool.call(
            path=docs_path,
            recursive=True,
            chunk_large_documents=True
        )

        print("✅ Import completed!")
        print(f"📊 Import result: {result}")

        # Verify import by checking Solr
        print("� Verifying import...")
        import requests
        response = requests.get("http://localhost:8983/solr/bleen_documents/select?q=*:*&rows=0")
        if response.status_code == 200:
            data = response.json()
            num_docs = data['response']['numFound']
            print(f"✅ Verification successful: {num_docs} documents in Solr")
        else:
            print("⚠️  Could not verify import")

    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import_documents()
