"""
Core Agent implementation using LlamaIndex Agent framework.

This module implements the main Bleen Agent following best practices from:
- Anthropic's "Building Effective Agents" 
- OpenAI's "A Practical Guide to Building Agents"

Key principles implemented:
1. Clear tool definitions with explicit purposes
2. Structured reasoning with step-by-step thinking
3. Proper error handling and recovery
4. Memory management for conversation context
5. Tool orchestration with proper selection logic
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import uuid

from llama_index.core.agent import ReActAgent
from llama_index.core.tools import BaseTool
from llama_index.llms.openai import OpenAI
from llama_index.core.memory import ChatMemoryBuffer
from llama_index.core.callbacks import CallbackManager, LlamaDebugHandler

from ..core.config import settings
from ..memory.conversation import ConversationMemory
from ..utils.logging import AgentLogger

logger = AgentLogger(__name__)


class BleenAgent:
    """
    Main Bleen Agent that orchestrates document search and question answering.
    
    This agent follows the ReAct (Reasoning + Acting) pattern:
    1. Reason about the user's request
    2. Select appropriate tools
    3. Execute tools with proper error handling
    4. Synthesize results into coherent responses
    """
    
    def __init__(self, tools: List[BaseTool] = None):
        """
        Initialize the Bleen Agent.
        
        Args:
            tools: List of tools available to the agent
        """
        self.session_id = self._generate_session_id()
        self.tools = tools or []
        
        logger.log_step("Initializing Bleen Agent", {
            "session_id": self.session_id,
            "num_tools": len(self.tools),
            "model": settings.llm_model
        })
        
        # Initialize LLM
        self.llm = OpenAI(
            model=settings.llm_model,
            temperature=settings.llm_temperature,
            max_tokens=settings.llm_max_tokens,
            api_key=settings.openai_api_key
        )
        
        # Initialize memory system
        self.memory = ConversationMemory(
            max_tokens=settings.memory_max_tokens,
            llm=self.llm
        )
        
        # Initialize callback manager for debugging
        self.callback_manager = CallbackManager([LlamaDebugHandler()])
        
        # Initialize the ReAct agent
        self._initialize_agent()
        
        logger.log_step("Agent Initialization Complete", {
            "session_id": self.session_id,
            "tools_available": [tool.metadata.name for tool in self.tools]
        })
    
    def _initialize_agent(self) -> None:
        """Initialize the ReAct agent with tools and memory."""
        
        # Create system prompt following best practices
        system_prompt = self._create_system_prompt()
        
        # Initialize chat memory buffer
        chat_memory = ChatMemoryBuffer.from_defaults(
            token_limit=settings.memory_max_tokens
        )
        
        # Create the ReAct agent
        self.agent = ReActAgent.from_tools(
            tools=self.tools,
            llm=self.llm,
            memory=chat_memory,
            system_prompt=system_prompt,
            max_iterations=settings.agent_max_iterations,
            verbose=settings.agent_verbose,
            callback_manager=self.callback_manager
        )
    
    def _create_system_prompt(self) -> str:
        """
        Create a comprehensive system prompt following best practices.
        
        Based on Anthropic and OpenAI guidelines:
        - Clear role definition
        - Explicit capabilities and limitations
        - Step-by-step reasoning instructions
        - Tool usage guidelines
        """
        return """You are Bleen Agent, an AI assistant specialized in document retrieval and question answering.

## Your Role
You help users find information from a document collection and provide accurate, well-sourced answers to their questions.

## Your Capabilities
- Search through documents using various strategies (keyword, semantic, filtered)
- Analyze document collections and provide statistics
- Import and manage documents
- Provide detailed explanations with source citations
- Maintain conversation context and follow-up on previous topics

## Your Approach
1. **Understand**: Carefully analyze what the user is asking for
2. **Plan**: Determine which tools and search strategies will be most effective
3. **Execute**: Use tools systematically, handling any errors gracefully
4. **Synthesize**: Combine results into a coherent, well-sourced response
5. **Verify**: Ensure your answer directly addresses the user's question

## Tool Usage Guidelines
- Always explain your reasoning before using tools
- Use the most specific tool for each task
- If a tool fails, try alternative approaches
- Cite sources clearly in your responses
- Ask for clarification if the user's request is ambiguous

## Response Format
- Provide direct answers to questions
- Include relevant source citations
- Explain your reasoning process when helpful
- Suggest follow-up questions or related topics when appropriate

## Limitations
- You can only access information from the indexed document collection
- You cannot browse the internet or access external resources
- You cannot modify or delete documents without explicit user permission

Remember: Be helpful, accurate, and transparent about your reasoning process."""
    
    async def ask(self, question: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user question and return a comprehensive response.
        
        Args:
            question: User's question
            context: Optional context for the conversation
            
        Returns:
            Dictionary containing answer, sources, reasoning, and metadata
        """
        start_time = datetime.utcnow()
        
        logger.log_step("Processing User Question", {
            "question": question,
            "session_id": self.session_id,
            "context": context or {}
        })
        
        try:
            # Add context to memory if provided
            if context:
                self.memory.add_context(context)
            
            # Process the question through the agent
            response = await self.agent.achat(question)
            
            # Extract reasoning steps from agent execution
            reasoning_steps = self._extract_reasoning_steps()
            
            # Get sources from tool calls
            sources = self._extract_sources_from_response(response)
            
            # Calculate processing time
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Create comprehensive response
            result = {
                "answer": str(response),
                "sources": sources,
                "reasoning_steps": reasoning_steps,
                "confidence": self._calculate_confidence(response, sources),
                "metadata": {
                    "session_id": self.session_id,
                    "processing_time_seconds": processing_time,
                    "timestamp": start_time.isoformat(),
                    "model_used": settings.llm_model,
                    "tools_used": self._get_tools_used(),
                    "token_usage": self._get_token_usage(),
                    "num_sources": len(sources)
                }
            }
            
            # Store in memory
            self.memory.add_interaction(question, result)
            
            logger.log_step("Question Processing Complete", {
                "processing_time": processing_time,
                "confidence": result["confidence"],
                "tools_used": result["metadata"]["tools_used"]
            })
            
            return result
            
        except Exception as e:
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            logger.error("Question processing failed", 
                        question=question, 
                        error=str(e),
                        processing_time=processing_time)
            
            return {
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "sources": [],
                "reasoning_steps": [],
                "confidence": 0.0,
                "metadata": {
                    "session_id": self.session_id,
                    "processing_time_seconds": processing_time,
                    "timestamp": start_time.isoformat(),
                    "error": str(e),
                    "num_sources": 0
                }
            }
    
    def add_tool(self, tool: BaseTool) -> None:
        """Add a new tool to the agent."""
        self.tools.append(tool)
        # Reinitialize agent with new tools
        self._initialize_agent()
        
        logger.log_step("Tool Added", {
            "tool_name": tool.metadata.name,
            "total_tools": len(self.tools)
        })
    
    def remove_tool(self, tool_name: str) -> bool:
        """Remove a tool from the agent."""
        initial_count = len(self.tools)
        self.tools = [tool for tool in self.tools if tool.metadata.name != tool_name]
        
        if len(self.tools) < initial_count:
            self._initialize_agent()
            logger.log_step("Tool Removed", {
                "tool_name": tool_name,
                "total_tools": len(self.tools)
            })
            return True
        
        return False
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get the conversation history."""
        return self.memory.get_history()
    
    def clear_conversation_history(self) -> None:
        """Clear the conversation history."""
        self.memory.clear()
        # Reset agent memory as well
        self.agent.memory.reset()
        
        logger.log_step("Conversation History Cleared", {
            "session_id": self.session_id
        })
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current agent status and statistics."""
        return {
            "session_id": self.session_id,
            "tools_available": [
                {
                    "name": tool.metadata.name,
                    "description": tool.metadata.description
                }
                for tool in self.tools
            ],
            "memory_usage": self.memory.get_usage_stats(),
            "model_config": {
                "model": settings.llm_model,
                "temperature": settings.llm_temperature,
                "max_tokens": settings.llm_max_tokens
            },
            "conversation_length": len(self.memory.get_history())
        }
    
    def _generate_session_id(self) -> str:
        """Generate a unique session ID."""
        return f"session_{uuid.uuid4().hex[:8]}_{int(datetime.utcnow().timestamp())}"
    
    def _extract_reasoning_steps(self) -> List[Dict[str, Any]]:
        """Extract reasoning steps from agent execution."""
        # This would extract the reasoning steps from the agent's execution
        # For now, return empty list - will be implemented with proper callback handling
        return []
    
    def _extract_sources_from_response(self, response) -> List[Dict[str, Any]]:
        """Extract source information from the agent response."""
        # This would extract sources from tool calls
        # For now, return empty list - will be implemented with tool integration
        return []
    
    def _calculate_confidence(self, response, sources: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for the response."""
        # Simple confidence calculation based on sources and response length
        if not sources:
            return 0.3
        
        response_text = str(response)
        if len(response_text) < 50:
            return 0.4
        
        # Higher confidence with more sources and longer responses
        source_factor = min(len(sources) * 0.2, 0.5)
        length_factor = min(len(response_text) / 1000, 0.3)
        
        return min(0.6 + source_factor + length_factor, 1.0)
    
    def _get_tools_used(self) -> List[str]:
        """Get list of tools used in the last interaction."""
        # This would track tools used during execution
        # For now, return empty list - will be implemented with proper callback handling
        return []
    
    def _get_token_usage(self) -> Dict[str, int]:
        """Get token usage statistics."""
        # This would track token usage
        # For now, return empty dict - will be implemented with proper callback handling
        return {}
