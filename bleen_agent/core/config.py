"""
Configuration management for Bleen Agent using Pydantic Settings.
"""

from typing import Optional, List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # OpenAI Configuration
    openai_api_key: str = Field(..., description="OpenAI API key")

    # Solr Configuration
    solr_url: str = Field(default="http://localhost:8983/solr", description="Solr server URL")
    solr_collection: str = Field(default="bleen", description="Solr collection name")

    # Agent Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    max_search_results: int = Field(default=10, description="Maximum number of search results to retrieve")
    agent_max_iterations: int = Field(default=10, description="Maximum iterations for agent reasoning")
    agent_verbose: bool = Field(default=True, description="Enable verbose agent logging")

    # LLM Configuration
    llm_model: str = Field(default="gpt-4-turbo-preview", description="OpenAI model to use for agent")
    llm_temperature: float = Field(default=0.1, description="LLM temperature for response generation")
    llm_max_tokens: int = Field(default=4000, description="Maximum tokens for LLM response")

    # Tool Configuration
    tool_timeout: int = Field(default=30, description="Timeout for tool execution in seconds")
    enable_tool_caching: bool = Field(default=True, description="Enable caching for tool results")

    # Memory Configuration
    memory_max_tokens: int = Field(default=8000, description="Maximum tokens to keep in memory")
    memory_summary_model: str = Field(default="gpt-3.5-turbo", description="Model for memory summarization")

    # Document Processing
    max_chunk_size: int = Field(default=2000, description="Maximum chunk size for document splitting")
    chunk_overlap: int = Field(default=200, description="Overlap between chunks")
    # Simplified: just use default values, no env parsing
    supported_file_types: List[str] = [".md", ".txt", ".pdf", ".docx"]

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()
