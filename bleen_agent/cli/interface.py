"""
Interactive command-line interface for <PERSON><PERSON>.

Redesigned to work with the new tool-based agent architecture.
Provides a chat-like interface with enhanced agent reasoning visibility.
"""

import sys
import asyncio
from typing import Dict, Any, Optional, List
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.markdown import Markdown
from rich.prompt import Prompt, Confirm
from rich.text import Text
from rich.columns import Columns
from rich.progress import Progress, SpinnerColumn, TextColumn

from ..agent.factory import create_agent_with_health_check, create_preset_agent, AGENT_PRESETS
from ..core.config import settings
from ..utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


class BleenCLI:
    """
    Interactive command-line interface for <PERSON>leen Agent.

    Enhanced to work with the new tool-based agent architecture.
    """

    def __init__(self, agent_type: str = "full"):
        self.console = Console()
        self.agent = None
        self.running = False
        self.agent_type = agent_type

        # CL<PERSON> commands
        self.commands = {
            'help': self._show_help,
            'stats': self._show_stats,
            'history': self._show_history,
            'clear': self._clear_history,
            'tools': self._show_tools,
            'agent': self._show_agent_info,
            'preset': self._switch_preset,
            'import': self._import_documents,
            'settings': self._show_settings,
            'quit': self._quit,
            'exit': self._quit
        }
    
    def start(self) -> None:
        """Start the interactive CLI."""
        self._show_welcome()

        try:
            # Initialize agent with health check
            self.console.print("[dim]Initializing Bleen Agent with health check...[/dim]")

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Checking system health...", total=None)

                if self.agent_type in AGENT_PRESETS:
                    self.agent = create_preset_agent(self.agent_type)
                else:
                    self.agent = create_agent_with_health_check()

                progress.update(task, completed=True)

            if not self.agent:
                self.console.print("[bold red]✗[/bold red] System health check failed!")
                self.console.print("[dim]Please check Solr connection and try again[/dim]")
                sys.exit(1)

            self.running = True
            self.console.print("[green]✓[/green] Agent initialized successfully!")
            self._show_agent_summary()
            self.console.print()

            # Main interaction loop
            self._interaction_loop()

        except Exception as e:
            self.console.print(f"[bold red]Error:[/bold red] Failed to initialize agent: {str(e)}")
            sys.exit(1)
    
    def _interaction_loop(self) -> None:
        """Main interaction loop."""
        while self.running:
            try:
                # Get user input
                user_input = Prompt.ask(
                    "[bold blue]You[/bold blue]",
                    default="",
                    show_default=False
                ).strip()
                
                if not user_input:
                    continue
                
                # Check if it's a command
                if user_input.startswith('/'):
                    self._handle_command(user_input[1:])
                else:
                    # Process as a question
                    self._handle_question(user_input)
                
                self.console.print()
                
            except KeyboardInterrupt:
                self.console.print("\n[dim]Use /quit to exit[/dim]")
            except EOFError:
                break
        
        self._show_goodbye()
    
    def _handle_question(self, question: str) -> None:
        """Handle a user question."""
        self.console.print("[dim]Processing your question...[/dim]")

        try:
            # Show progress while agent is thinking
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Agent is reasoning...", total=None)

                # Get answer from agent (async)
                response = asyncio.run(self.agent.ask(question))

                progress.update(task, completed=True)

            # Display answer
            self._display_answer(response)

        except Exception as e:
            self.console.print(f"[bold red]Error:[/bold red] {str(e)}")
    
    def _handle_command(self, command_line: str) -> None:
        """Handle a CLI command."""
        parts = command_line.split()
        if not parts:
            return
        
        command = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        if command in self.commands:
            try:
                self.commands[command](args)
            except Exception as e:
                self.console.print(f"[bold red]Command error:[/bold red] {str(e)}")
        else:
            self.console.print(f"[yellow]Unknown command:[/yellow] {command}")
            self.console.print("Type [bold]/help[/bold] for available commands")
    
    def _display_answer(self, response: Dict[str, Any]) -> None:
        """Display the agent's answer with formatting."""
        # Main answer
        answer_panel = Panel(
            Markdown(response["answer"]),
            title="[bold green]Bleen Agent[/bold green]",
            border_style="green"
        )
        self.console.print(answer_panel)
        
        # Confidence and metadata
        confidence = response.get("confidence", 0.0)
        confidence_color = "green" if confidence > 0.7 else "yellow" if confidence > 0.4 else "red"
        
        metadata_text = Text()
        metadata_text.append(f"Confidence: ", style="dim")
        metadata_text.append(f"{confidence:.1%}", style=confidence_color)
        metadata_text.append(f" | Processing time: ", style="dim")
        metadata_text.append(f"{response['metadata']['processing_time_seconds']:.2f}s", style="cyan")
        metadata_text.append(f" | Sources: ", style="dim")
        metadata_text.append(f"{response['metadata']['num_sources']}", style="blue")

        # Add tools used if available
        tools_used = response['metadata'].get('tools_used', [])
        if tools_used:
            metadata_text.append(f" | Tools used: ", style="dim")
            metadata_text.append(f"{', '.join(tools_used)}", style="green")

        # Add document chunks info if available
        num_chunks = response['metadata'].get('num_document_chunks', 0)
        if num_chunks > 0:
            metadata_text.append(f" | Chunks: ", style="dim")
            metadata_text.append(f"{num_chunks}", style="yellow")

        self.console.print(metadata_text)
        
        # Sources (if any)
        if response.get("sources"):
            self._display_sources(response["sources"])

        # Tool usage details (if verbose or tools were used)
        if response['metadata'].get('tools_used'):
            self._display_tool_usage_details(response['metadata'])

        # Document chunks details (if any)
        if response['metadata'].get('document_chunks_used'):
            self._display_document_chunks(response['metadata']['document_chunks_used'])
        
        # Query analysis (if verbose mode or low confidence)
        if confidence < 0.5 or response.get("query_analysis"):
            self._display_query_analysis(response.get("query_analysis"))

        # Show reasoning steps if available
        if response.get("reasoning_steps"):
            self._display_reasoning_steps(response["reasoning_steps"])
    
    def _display_sources(self, sources: list) -> None:
        """Display source information."""
        if not sources:
            return
        
        self.console.print("\n[bold]Sources:[/bold]")
        
        for i, source in enumerate(sources[:5], 1):  # Show max 5 sources
            source_text = Text()
            source_text.append(f"{i}. ", style="dim")
            source_text.append(source.get("title", "Untitled"), style="bold")
            
            if source.get("source_file"):
                source_text.append(f" ({source['source_file']})", style="dim")
            
            source_text.append(f" [Score: {source.get('relevance_score', 0):.2f}]", style="cyan")
            
            self.console.print(source_text)
            
            # Show preview
            preview = source.get("preview", "")
            if preview:
                self.console.print(f"   [dim]{preview}[/dim]")
    
    def _display_query_analysis(self, analysis: Optional[Dict[str, Any]]) -> None:
        """Display query analysis information."""
        if not analysis:
            return
        
        self.console.print("\n[bold]Query Analysis:[/bold]")
        
        analysis_table = Table(show_header=False, box=None, padding=(0, 1))
        analysis_table.add_column("Key", style="cyan")
        analysis_table.add_column("Value", style="white")
        
        analysis_table.add_row("Query Type", analysis.get("query_type", "unknown"))
        analysis_table.add_row("Search Strategy", analysis.get("search_strategy", "unknown"))
        analysis_table.add_row("Key Terms", ", ".join(analysis.get("key_terms", [])))
        
        if analysis.get("filters"):
            filters_str = ", ".join(f"{k}:{v}" for k, v in analysis["filters"].items())
            analysis_table.add_row("Filters", filters_str)
        
        self.console.print(analysis_table)

    def _display_reasoning_steps(self, reasoning_steps: List[Dict[str, Any]]) -> None:
        """Display agent reasoning steps."""
        if not reasoning_steps:
            return

        self.console.print("\n[bold]Agent Reasoning:[/bold]")

        for i, step in enumerate(reasoning_steps, 1):
            step_text = Text()
            step_text.append(f"{i}. ", style="cyan")
            step_text.append(step.get("action", "Unknown action"), style="bold")

            if step.get("reasoning"):
                step_text.append(f": {step['reasoning']}", style="dim")

            self.console.print(step_text)

    def _show_agent_summary(self) -> None:
        """Show agent configuration summary."""
        if not self.agent:
            return

        status = self.agent.get_agent_status()

        summary_table = Table(title="Agent Configuration", show_header=False)
        summary_table.add_column("Property", style="cyan")
        summary_table.add_column("Value", style="white")

        summary_table.add_row("Session ID", status["session_id"])
        summary_table.add_row("Model", status["model_config"]["model"])
        summary_table.add_row("Available Tools", str(len(status["tools_available"])))
        summary_table.add_row("Memory Max Tokens", str(status["model_config"]["max_tokens"]))

        self.console.print(summary_table)
    
    def _show_help(self, args: list) -> None:
        """Show help information."""
        help_table = Table(title="Bleen Agent Commands")
        help_table.add_column("Command", style="cyan")
        help_table.add_column("Description", style="white")

        help_table.add_row("/help", "Show this help message")
        help_table.add_row("/stats", "Show collection statistics")
        help_table.add_row("/history", "Show conversation history")
        help_table.add_row("/clear", "Clear conversation history")
        help_table.add_row("/tools", "Show available tools (categorized by type)")
        help_table.add_row("/agent", "Show agent information")
        help_table.add_row("/preset <name>", "Switch to a preset agent configuration")
        help_table.add_row("/import <path>", "Import documents from file or directory")
        help_table.add_row("/settings", "Show current settings")
        help_table.add_row("/quit or /exit", "Exit the application")

        self.console.print(help_table)
        self.console.print("\n[dim]You can also just type your questions directly![/dim]")
        self.console.print("[dim]💡 Use [bold]/tools[/bold] to see detailed information about available capabilities![/dim]")
    
    def _show_stats(self, args: list) -> None:
        """Show collection statistics."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return
        
        self.console.print("[dim]Getting collection statistics...[/dim]")
        
        try:
            stats = self.agent.get_collection_stats()
            
            stats_table = Table(title="Collection Statistics")
            stats_table.add_column("Metric", style="cyan")
            stats_table.add_column("Value", style="green")
            
            stats_table.add_row("Total Documents", str(stats.get("total_documents", 0)))
            
            # Document types
            if "document_types" in stats:
                for doc_type, count in stats["document_types"].items():
                    stats_table.add_row(f"Type: {doc_type}", str(count))
            
            # Chunks vs documents
            if "chunks_vs_documents" in stats:
                for chunk_type, count in stats["chunks_vs_documents"].items():
                    label = "Chunks" if chunk_type == "true" else "Full Documents"
                    stats_table.add_row(label, str(count))
            
            self.console.print(stats_table)
            
        except Exception as e:
            self.console.print(f"[red]Error getting stats: {str(e)}[/red]")
    
    def _show_history(self, args: list) -> None:
        """Show conversation history."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return
        
        history = self.agent.get_conversation_history()
        
        if not history:
            self.console.print("[dim]No conversation history[/dim]")
            return
        
        self.console.print(f"[bold]Conversation History ({len(history)} entries):[/bold]\n")
        
        for i, entry in enumerate(history[-10:], 1):  # Show last 10 entries
            # Question
            self.console.print(f"[bold blue]{i}. You:[/bold blue] {entry['question']}")
            
            # Answer (truncated)
            answer = entry['answer']
            if len(answer) > 200:
                answer = answer[:200] + "..."
            
            self.console.print(f"[bold green]   Agent:[/bold green] {answer}")
            
            # Metadata
            confidence = entry.get('confidence', 0.0)
            confidence_color = "green" if confidence > 0.7 else "yellow" if confidence > 0.4 else "red"
            
            metadata_text = Text()
            metadata_text.append("   ", style="dim")
            metadata_text.append(f"Confidence: {confidence:.1%}", style=confidence_color)
            metadata_text.append(f" | Sources: {entry.get('num_sources', 0)}", style="dim")
            
            self.console.print(metadata_text)
            self.console.print()
    
    def _clear_history(self, args: list) -> None:
        """Clear conversation history."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return
        
        if Confirm.ask("Are you sure you want to clear the conversation history?"):
            self.agent.clear_conversation_history()
            self.console.print("[green]✓[/green] Conversation history cleared")
    
    def _search_documents(self, args: list) -> None:
        """Search documents directly."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return
        
        if not args:
            query = Prompt.ask("Enter search query")
        else:
            query = " ".join(args)
        
        if not query:
            return
        
        self.console.print(f"[dim]Searching for: {query}[/dim]")
        
        try:
            results = self.agent.search_documents(query)
            
            if not results["results"]:
                self.console.print("[yellow]No results found[/yellow]")
                return
            
            self.console.print(f"[bold]Found {len(results['results'])} results:[/bold]\n")
            
            for i, result in enumerate(results["results"][:10], 1):
                self.console.print(f"[bold]{i}. {result['title']}[/bold]")
                self.console.print(f"   ID: {result['id']}")
                self.console.print(f"   Score: {result['relevance_score']:.3f}")
                self.console.print(f"   Preview: [dim]{result['content_preview']}[/dim]")
                self.console.print()
                
        except Exception as e:
            self.console.print(f"[red]Search error: {str(e)}[/red]")
    
    def _find_similar(self, args: list) -> None:
        """Find similar documents."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return
        
        if not args:
            doc_id = Prompt.ask("Enter document ID")
        else:
            doc_id = args[0]
        
        if not doc_id:
            return
        
        self.console.print(f"[dim]Finding documents similar to: {doc_id}[/dim]")
        
        try:
            results = self.agent.get_similar_documents(doc_id)
            
            similar_docs = results.get("similar_documents", [])
            
            if not similar_docs:
                self.console.print("[yellow]No similar documents found[/yellow]")
                return
            
            self.console.print(f"[bold]Found {len(similar_docs)} similar documents:[/bold]\n")
            
            for i, doc in enumerate(similar_docs, 1):
                self.console.print(f"[bold]{i}. {doc['title']}[/bold]")
                self.console.print(f"   ID: {doc['id']}")
                self.console.print(f"   Similarity: {doc['relevance_score']:.3f}")
                self.console.print(f"   Preview: [dim]{doc['content_preview']}[/dim]")
                self.console.print()
                
        except Exception as e:
            self.console.print(f"[red]Error finding similar documents: {str(e)}[/red]")
    
    def _show_settings(self, args: list) -> None:
        """Show current settings."""
        settings_table = Table(title="Current Settings")
        settings_table.add_column("Setting", style="cyan")
        settings_table.add_column("Value", style="white")
        
        settings_table.add_row("Solr URL", settings.solr_url)
        settings_table.add_row("Collection", settings.solr_collection)
        settings_table.add_row("LLM Model", settings.llm_model)
        settings_table.add_row("Max Results", str(settings.max_search_results))
        settings_table.add_row("Chunk Size", str(settings.max_chunk_size))
        settings_table.add_row("Log Level", settings.log_level)
        
        self.console.print(settings_table)
    
    def _show_tools(self, args: list) -> None:
        """Show available tools with detailed information."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return

        status = self.agent.get_agent_status()
        tools = status["tools_available"]

        if not tools:
            self.console.print("[yellow]No tools available[/yellow]")
            return

        # Group tools by category
        tool_categories = {
            "🔍 Search Tools": [],
            "📁 Management Tools": [],
            "📊 Analysis Tools": []
        }

        for tool in tools:
            name = tool["name"]
            if name in ["document_search", "filtered_search", "find_similar_documents"]:
                tool_categories["🔍 Search Tools"].append(tool)
            elif name in ["import_documents", "delete_document", "get_document_details"]:
                tool_categories["📁 Management Tools"].append(tool)
            elif name in ["analyze_query", "get_collection_stats"]:
                tool_categories["📊 Analysis Tools"].append(tool)

        # Display tools by category
        for category, category_tools in tool_categories.items():
            if category_tools:
                self.console.print(f"\n[bold blue]{category}[/bold blue]")

                tools_table = Table(show_header=False, box=None, padding=(0, 2))
                tools_table.add_column("Tool", style="cyan", no_wrap=True, width=25)
                tools_table.add_column("Description", style="white")

                for tool in category_tools:
                    # Truncate long descriptions for better display
                    description = tool["description"]
                    if len(description) > 80:
                        description = description[:77] + "..."

                    tools_table.add_row(f"• {tool['name']}", description)

                self.console.print(tools_table)

        # Show usage statistics if available
        if hasattr(self.agent, 'tools_used_in_session') and self.agent.tools_used_in_session:
            used_tools = list(set(self.agent.tools_used_in_session))
            self.console.print(f"\n[dim]🔧 Tools used in this session: {', '.join(used_tools)}[/dim]")

    def _show_agent_info(self, args: list) -> None:
        """Show detailed agent information."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return

        status = self.agent.get_agent_status()

        # Agent info
        agent_table = Table(title="Agent Information")
        agent_table.add_column("Property", style="cyan")
        agent_table.add_column("Value", style="white")

        agent_table.add_row("Session ID", status["session_id"])
        agent_table.add_row("Model", status["model_config"]["model"])
        agent_table.add_row("Temperature", str(status["model_config"]["temperature"]))
        agent_table.add_row("Max Tokens", str(status["model_config"]["max_tokens"]))
        agent_table.add_row("Available Tools", str(len(status["tools_available"])))
        agent_table.add_row("Conversation Length", str(status["conversation_length"]))

        self.console.print(agent_table)

        # Memory usage
        memory_stats = status["memory_usage"]
        memory_table = Table(title="Memory Usage")
        memory_table.add_column("Metric", style="cyan")
        memory_table.add_column("Value", style="white")

        memory_table.add_row("Total Interactions", str(memory_stats["total_interactions"]))
        memory_table.add_row("Current Tokens", str(memory_stats["current_tokens"]))
        memory_table.add_row("Max Tokens", str(memory_stats["max_tokens"]))
        memory_table.add_row("Memory Utilization", f"{memory_stats['memory_utilization']:.1%}")

        self.console.print(memory_table)

    def _switch_preset(self, args: list) -> None:
        """Switch to a preset agent configuration."""
        if not args:
            # Show available presets
            presets_table = Table(title="Available Agent Presets")
            presets_table.add_column("Preset", style="cyan")
            presets_table.add_column("Description", style="white")

            for preset_name, config in AGENT_PRESETS.items():
                presets_table.add_row(preset_name, config["description"])

            self.console.print(presets_table)
            return

        preset_name = args[0]

        if preset_name not in AGENT_PRESETS:
            self.console.print(f"[red]Unknown preset: {preset_name}[/red]")
            self.console.print("Use [bold]/preset[/bold] without arguments to see available presets")
            return

        try:
            self.console.print(f"[dim]Switching to preset: {preset_name}[/dim]")

            # Create new agent with preset
            new_agent = create_preset_agent(preset_name)

            # Transfer conversation history if desired
            if self.agent and Confirm.ask("Transfer conversation history to new agent?"):
                history = self.agent.get_conversation_history()
                for interaction in history:
                    new_agent.memory.add_interaction(
                        interaction["question"],
                        {"answer": interaction["answer"]}
                    )

            self.agent = new_agent
            self.agent_type = preset_name

            self.console.print(f"[green]✓[/green] Switched to {preset_name} preset")
            self._show_agent_summary()

        except Exception as e:
            self.console.print(f"[red]Failed to switch preset: {str(e)}[/red]")

    def _import_documents(self, args: list) -> None:
        """Import documents using the agent."""
        if not self.agent:
            self.console.print("[red]Agent not initialized[/red]")
            return

        if not args:
            path = Prompt.ask("Enter path to file or directory")
        else:
            path = " ".join(args)

        if not path:
            return

        try:
            self.console.print(f"[dim]Importing documents from: {path}[/dim]")

            # Use the agent to import documents
            question = f"Import documents from {path}"
            response = asyncio.run(self.agent.ask(question))

            self.console.print("[bold green]Import Result:[/bold green]")
            self.console.print(response["answer"])

        except Exception as e:
            self.console.print(f"[red]Import failed: {str(e)}[/red]")

    def _quit(self, args: list) -> None:
        """Quit the application."""
        self.running = False
    
    def _show_welcome(self) -> None:
        """Show welcome message."""
        welcome_text = f"""
[bold blue]Welcome to Bleen Agent![/bold blue]

An AI-powered document retrieval and question answering system built with LlamaIndex.

[bold]Agent Type:[/bold] {self.agent_type}

[dim]Commands start with / (e.g., /help)
Just type your questions directly to get answers!
Use /tools to see available capabilities.[/dim]
        """

        welcome_panel = Panel(
            welcome_text.strip(),
            title="[bold]Bleen Agent v0.2.0[/bold]",
            border_style="blue"
        )

        self.console.print(welcome_panel)
        self.console.print()
    
    def _display_tool_usage_details(self, metadata: dict) -> None:
        """Display detailed tool usage information."""
        tool_calls = metadata.get('tool_calls_detail', [])
        if not tool_calls:
            return

        self.console.print("\n[bold]🔧 Tool Usage Details:[/bold]")

        for i, call in enumerate(tool_calls, 1):
            tool_name = call.get('tool_name', 'Unknown')
            arguments = call.get('arguments', {})

            # Create a simple display of arguments
            args_str = ""
            if isinstance(arguments, dict):
                args_items = []
                for key, value in arguments.items():
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:47] + "..."
                    args_items.append(f"{key}={value}")
                args_str = ", ".join(args_items)

            self.console.print(f"  [cyan]{i}.[/cyan] [green]{tool_name}[/green]")
            if args_str:
                self.console.print(f"     [dim]Arguments: {args_str}[/dim]")

    def _display_document_chunks(self, chunks: list) -> None:
        """Display document chunks that were consulted."""
        if not chunks:
            return

        self.console.print("\n[bold]📄 Document Chunks Consulted:[/bold]")

        chunks_table = Table(show_header=False, box=None, padding=(0, 2))
        chunks_table.add_column("Document", style="cyan", width=25)
        chunks_table.add_column("Preview", style="white", width=60)
        chunks_table.add_column("Score", style="yellow", width=8)

        for chunk in chunks[:5]:  # Show max 5 chunks
            title = chunk.get('title', 'Unknown Document')
            preview = chunk.get('content_preview', 'No preview available')
            score = chunk.get('score', 0)

            # Truncate title if too long
            if len(title) > 22:
                title = title[:19] + "..."

            chunks_table.add_row(title, preview, f"{score:.2f}")

        self.console.print(chunks_table)

        if len(chunks) > 5:
            self.console.print(f"[dim]... and {len(chunks) - 5} more chunks[/dim]")

    def _show_goodbye(self) -> None:
        """Show goodbye message."""
        self.console.print("\n[bold blue]Thank you for using Bleen Agent![/bold blue]")
        self.console.print("[dim]Goodbye![/dim]")
