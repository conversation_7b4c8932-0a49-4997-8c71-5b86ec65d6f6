"""
Document management tools for Bleen Agent.

Provides tools for:
- Importing documents from files and directories
- Deleting documents from the collection
- Updating document metadata
- Getting document details
"""

import os
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
from datetime import datetime

import pysolr
import frontmatter
import markdown
from docx import Document
from llama_index.core.tools import BaseTool
from llama_index.core.tools.types import ToolMetadata
from llama_index.llms.openai import OpenAI

from ..core.config import settings
from ..utils.logging import get_logger

logger = get_logger(__name__)


class DocumentImportTool(BaseTool):
    """
    Tool for importing documents into the collection.

    Supports:
    - Single file import
    - Directory import (recursive)
    - Markdown files with frontmatter
    - Plain text files
    - Microsoft Word documents (.docx)
    - Automatic metadata extraction
    """

    def __init__(self):
        self.solr_url = f"{settings.solr_url}/{settings.solr_collection}"
        self.solr = pysolr.Solr(self.solr_url, always_commit=True)
        # Initialize LLM for intelligent attribute extraction
        self.llm = OpenAI(
            model="gpt-4-turbo-preview",
            api_key=settings.openai_api_key,
            temperature=0.1  # Low temperature for consistent extraction
        )
        super().__init__()

    @property
    def metadata(self):
        """Return tool metadata."""
        return ToolMetadata(
            name="import_documents",
            description=(
                "Import documents from files or directories into the collection. "
                "Supports Markdown files with frontmatter, plain text files, and Microsoft Word documents (.docx). "
                "Automatically extracts metadata like title, author, categories, and tags. "
                "Can import single files or entire directories recursively."
            )
        )

    def __call__(self, *args, **kwargs) -> str:
        """Make the tool callable."""
        return self.call(*args, **kwargs)

    def call(
        self,
        path: str = None,
        input: str = None,
        recursive: bool = True,
        chunk_large_documents: bool = True,
        **kwargs
    ) -> str:
        """
        Import documents from a file or directory.

        Args:
            path: Path to file or directory to import
            input: Alternative parameter name for path (for compatibility)
            recursive: If directory, whether to scan recursively
            chunk_large_documents: Whether to split large documents into chunks

        Returns:
            JSON string with import results
        """
        # Handle both 'path' and 'input' parameter names
        import_path = path or input
        if not import_path:
            return json.dumps({
                "error": "No path provided. Use 'path' or 'input' parameter.",
                "imported_files": [],
                "total_documents": 0,
                "total_chunks": 0
            })
        try:
            logger.info("Starting document import", path=import_path, recursive=recursive)

            path_obj = Path(import_path)
            
            if not path_obj.exists():
                return json.dumps({
                    "error": f"Path does not exist: {import_path}",
                    "imported": 0,
                    "failed": 0
                })
            
            if path_obj.is_file():
                # Import single file
                result = self._import_file(path_obj, chunk_large_documents)
                response_data = {
                    "path": import_path,
                    "type": "file",
                    "imported": 1 if result["success"] else 0,
                    "failed": 0 if result["success"] else 1,
                    "details": [result]
                }

                # Create a response object with raw_output attribute for OpenAI Agent compatibility
                class ToolResponse:
                    def __init__(self, data):
                        self.raw_output = json.dumps(data, indent=2)
                        self.data = data

                    def __str__(self):
                        return self.raw_output

                return ToolResponse(response_data)
            
            elif path_obj.is_dir():
                # Import directory
                return self._import_directory(path_obj, recursive, chunk_large_documents)
            
            else:
                error_data = {
                    "error": f"Path is neither file nor directory: {path}",
                    "imported": 0,
                    "failed": 0
                }

                # Create a response object with raw_output attribute for OpenAI Agent compatibility
                class ToolResponse:
                    def __init__(self, data):
                        self.raw_output = json.dumps(data, indent=2)
                        self.data = data

                    def __str__(self):
                        return self.raw_output

                return ToolResponse(error_data)
                
        except Exception as e:
            logger.error("Document import failed", path=path, error=str(e))
            error_data = {
                "error": f"Import failed: {str(e)}",
                "path": path,
                "imported": 0,
                "failed": 1
            }

            # Create a response object with raw_output attribute for OpenAI Agent compatibility
            class ToolResponse:
                def __init__(self, data):
                    self.raw_output = json.dumps(data, indent=2)
                    self.data = data

                def __str__(self):
                    return self.raw_output

            return ToolResponse(error_data)
    
    def _import_directory(self, directory: Path, recursive: bool, chunk_large_documents: bool) -> str:
        """Import all supported files from a directory."""
        imported = 0
        failed = 0
        details = []
        
        # Find all supported files
        pattern = "**/*" if recursive else "*"
        
        for file_path in directory.glob(pattern):
            if file_path.is_file() and file_path.suffix.lower() in settings.supported_file_types:
                result = self._import_file(file_path, chunk_large_documents)
                details.append(result)
                
                if result["success"]:
                    imported += 1
                else:
                    failed += 1
        
        response_data = {
            "path": str(directory),
            "type": "directory",
            "recursive": recursive,
            "imported": imported,
            "failed": failed,
            "total_processed": imported + failed,
            "details": details[:10]  # Limit details to first 10 for readability
        }

        # Create a response object with raw_output attribute for OpenAI Agent compatibility
        class ToolResponse:
            def __init__(self, data):
                self.raw_output = json.dumps(data, indent=2)
                self.data = data

            def __str__(self):
                return self.raw_output

        return ToolResponse(response_data)
    
    def _import_file(self, file_path: Path, chunk_large_documents: bool = False) -> Dict[str, Any]:
        """Import a single file."""
        try:
            # Determine file type and parse
            if file_path.suffix.lower() in ['.md', '.markdown']:
                document = self._parse_markdown(file_path)
            elif file_path.suffix.lower() == '.txt':
                document = self._parse_text(file_path)
            elif file_path.suffix.lower() == '.docx':
                document = self._parse_docx(file_path)
            else:
                return {
                    "file": str(file_path),
                    "success": False,
                    "error": f"Unsupported file type: {file_path.suffix}"
                }
            
            # Force import as whole document (chunking disabled)
            return self._import_whole_document(document, file_path)
                
        except Exception as e:
            logger.error("File import failed", file_path=str(file_path), error=str(e))
            return {
                "file": str(file_path),
                "success": False,
                "error": str(e)
            }
    
    def _parse_markdown(self, file_path: Path) -> Dict[str, Any]:
        """Parse a Markdown file with frontmatter."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse frontmatter
        post = frontmatter.loads(content)
        metadata = post.metadata
        content_body = post.content
        
        # Generate document ID
        document_id = self._generate_document_id(file_path, content_body)
        
        # Extract metadata
        title = metadata.get('title') or self._extract_title_from_content(content_body) or file_path.stem
        
        # Get file stats
        file_stats = file_path.stat()
        
        # Extract intelligent attributes using AI
        ai_attributes = self._extract_intelligent_attributes(content_body, title, file_path.name)

        base_document = {
            "id": document_id,
            "document_id": document_id,
            "title": title,
            "content": content_body,
            "author": self._normalize_list_field(metadata.get('author') or metadata.get('authors')),
            "category": self._normalize_list_field(metadata.get('category') or metadata.get('categories')),
            "tags": self._normalize_list_field(metadata.get('tags') or metadata.get('tag')),
            "language": metadata.get('language', 'en'),
            "document_type": "markdown",
            "source_file": file_path.name,
            "file_path": str(file_path),
            "created_date": datetime.fromtimestamp(file_stats.st_ctime).isoformat() + "Z",
            "modified_date": datetime.fromtimestamp(file_stats.st_mtime).isoformat() + "Z",
            "indexed_date": datetime.utcnow().isoformat() + "Z",
            "content_length": len(content_body),
            "word_count": len(content_body.split()),
            "is_chunk": False,
            **{f"custom_{k}": v for k, v in metadata.items()
               if k not in ['title', 'author', 'authors', 'category', 'categories', 'tags', 'tag', 'language']}
        }

        # Merge AI-extracted attributes
        base_document.update(ai_attributes)

        return base_document
    
    def _parse_text(self, file_path: Path) -> Dict[str, Any]:
        """Parse a plain text file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        document_id = self._generate_document_id(file_path, content)
        title = self._extract_title_from_content(content) or file_path.stem
        
        file_stats = file_path.stat()
        
        # Extract intelligent attributes using AI
        ai_attributes = self._extract_intelligent_attributes(content, title, file_path.name)

        base_document = {
            "id": document_id,
            "document_id": document_id,
            "title": title,
            "content": content,
            "language": "en",
            "document_type": "text",
            "source_file": file_path.name,
            "file_path": str(file_path),
            "created_date": datetime.fromtimestamp(file_stats.st_ctime).isoformat() + "Z",
            "modified_date": datetime.fromtimestamp(file_stats.st_mtime).isoformat() + "Z",
            "indexed_date": datetime.utcnow().isoformat() + "Z",
            "content_length": len(content),
            "word_count": len(content.split()),
            "is_chunk": False
        }

        # Merge AI-extracted attributes
        base_document.update(ai_attributes)

        return base_document

    def _parse_docx(self, file_path: Path) -> Dict[str, Any]:
        """Parse a Microsoft Word document (.docx)."""
        try:
            # Load the document
            doc = Document(file_path)

            # Extract text content
            content_parts = []

            # Extract paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content_parts.append(paragraph.text.strip())

            # Extract tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        content_parts.append(" | ".join(row_text))

            content = "\n\n".join(content_parts)

            # Generate document ID
            document_id = self._generate_document_id(file_path, content)

            # Try to extract title from first paragraph or use filename
            title = None
            if doc.paragraphs and doc.paragraphs[0].text.strip():
                first_para = doc.paragraphs[0].text.strip()
                # If first paragraph is short and looks like a title
                if len(first_para) < 100 and not first_para.endswith('.'):
                    title = first_para

            if not title:
                title = file_path.stem

            # Get file stats
            file_stats = file_path.stat()

            # Try to extract metadata from document properties
            core_props = doc.core_properties
            author = []
            if core_props.author:
                author = [core_props.author]

            # Extract categories from subject if available
            category = []
            if core_props.subject:
                category = [core_props.subject]

            # Extract intelligent attributes using AI
            ai_attributes = self._extract_intelligent_attributes(content, title, file_path.name)

            base_document = {
                "id": document_id,
                "document_id": document_id,
                "title": title,
                "content": content,
                "author": author,
                "category": category,
                "tags": [],  # DOCX doesn't have built-in tags
                "language": "en",  # Default, could be enhanced with language detection
                "document_type": "docx",
                "source_file": file_path.name,
                "file_path": str(file_path),
                "created_date": datetime.fromtimestamp(file_stats.st_ctime).isoformat() + "Z",
                "modified_date": datetime.fromtimestamp(file_stats.st_mtime).isoformat() + "Z",
                "indexed_date": datetime.utcnow().isoformat() + "Z",
                "content_length": len(content),
                "word_count": len(content.split()),
                "is_chunk": False,
                # Add document properties as custom fields
                "custom_created": core_props.created.isoformat() + "Z" if core_props.created else None,
                "custom_modified": core_props.modified.isoformat() + "Z" if core_props.modified else None,
                "custom_last_modified_by": core_props.last_modified_by,
                "custom_revision": core_props.revision,
                "custom_keywords": core_props.keywords,
                "custom_comments": core_props.comments
            }

            # Merge AI-extracted attributes
            base_document.update(ai_attributes)

            return base_document

        except Exception as e:
            logger.error("DOCX parsing failed", file_path=str(file_path), error=str(e))
            # Fallback to basic file info
            file_stats = file_path.stat()
            document_id = self._generate_document_id(file_path, "")

            return {
                "id": document_id,
                "document_id": document_id,
                "title": file_path.stem,
                "content": f"[Error parsing DOCX file: {str(e)}]",
                "author": [],
                "category": [],
                "tags": [],
                "language": "en",
                "document_type": "docx",
                "source_file": file_path.name,
                "file_path": str(file_path),
                "created_date": datetime.fromtimestamp(file_stats.st_ctime).isoformat() + "Z",
                "modified_date": datetime.fromtimestamp(file_stats.st_mtime).isoformat() + "Z",
                "indexed_date": datetime.utcnow().isoformat() + "Z",
                "content_length": 0,
                "word_count": 0,
                "is_chunk": False,
                "custom_parse_error": str(e)
            }

    def _import_whole_document(self, document: Dict[str, Any], file_path: Path) -> Dict[str, Any]:
        """Import a document as a single entity."""
        try:
            self.solr.add([document])
            
            return {
                "file": str(file_path),
                "success": True,
                "document_id": document["document_id"],
                "title": document["title"],
                "type": "whole_document",
                "content_length": document["content_length"]
            }
            
        except Exception as e:
            return {
                "file": str(file_path),
                "success": False,
                "error": f"Solr indexing failed: {str(e)}"
            }
    
    def _import_with_chunks(self, document: Dict[str, Any], file_path: Path) -> Dict[str, Any]:
        """Import a document with chunking."""
        try:
            # Create chunks
            chunks = self._create_chunks(document)
            
            # Index original document
            self.solr.add([document])
            
            # Index chunks
            chunk_docs = []
            for i, chunk_content in enumerate(chunks):
                chunk_doc = document.copy()
                chunk_doc.update({
                    "id": f"{document['document_id']}_chunk_{i}",
                    "chunk_id": f"{document['document_id']}_chunk_{i}",
                    "chunk_index": i,
                    "content": chunk_content,
                    "content_length": len(chunk_content),
                    "word_count": len(chunk_content.split()),
                    "is_chunk": True
                })
                chunk_docs.append(chunk_doc)
            
            if chunk_docs:
                self.solr.add(chunk_docs)
            
            return {
                "file": str(file_path),
                "success": True,
                "document_id": document["document_id"],
                "title": document["title"],
                "type": "chunked_document",
                "chunks_created": len(chunks),
                "total_content_length": document["content_length"]
            }
            
        except Exception as e:
            return {
                "file": str(file_path),
                "success": False,
                "error": f"Chunked import failed: {str(e)}"
            }
    
    def _create_chunks(self, document: Dict[str, Any]) -> List[str]:
        """Create chunks from document content."""
        content = document["content"]
        max_size = settings.max_chunk_size
        overlap = settings.chunk_overlap
        
        # Simple chunking by paragraphs
        paragraphs = content.split('\n\n')
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            if len(current_chunk) + len(paragraph) <= max_size:
                current_chunk += paragraph + "\n\n"
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = paragraph + "\n\n"
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks

    def _extract_intelligent_attributes(self, content: str, title: str, file_name: str) -> Dict[str, Any]:
        """Extract intelligent attributes from document content using AI."""
        try:
            # Create a prompt for attribute extraction
            # Escape curly braces in content to avoid format string issues
            safe_content = content[:3000].replace('{', '{{').replace('}', '}}')
            safe_title = title.replace('{', '{{').replace('}', '}}')
            safe_file_name = file_name.replace('{', '{{').replace('}', '}}')

            prompt = f"""
Analizza il seguente documento e estrai gli attributi strutturati in formato JSON.

DOCUMENTO:
Titolo: {safe_title}
Nome file: {safe_file_name}
Contenuto: {safe_content}...

Estrai i seguenti attributi se presenti nel documento:

1. **abstract**: Riassunto breve del documento (max 200 parole)
2. **document_type_detailed**: Tipo specifico di documento (es: "DVR", "Contratto", "Manuale", "Relazione", "Organigramma")
3. **client_company**: Nome dell'azienda/cliente a cui si riferisce il documento
4. **roles_list**: Lista di ruoli con nominativi nel formato [{"role": "ruolo", "name": "nome"}]
5. **risk_categories**: Categorie di rischio menzionate (se applicabile)
6. **departments**: Reparti/settori aziendali menzionati
7. **key_topics**: Argomenti principali trattati (max 5)
8. **compliance_standards**: Standard normativi o di compliance citati
9. **document_status**: Stato del documento (es: "Bozza", "Approvato", "In revisione")
10. **revision_info**: Informazioni sulla revisione (numero, data)
11. **responsible_person**: Persona responsabile del documento
12. **approval_date**: Data di approvazione (se presente)
13. **validity_period**: Periodo di validità del documento
14. **related_documents**: Documenti correlati menzionati

IMPORTANTE:
- Restituisci SOLO un oggetto JSON valido
- Se un attributo non è presente, usa null
- Per le liste, usa array vuoti [] se non ci sono elementi
- Sii preciso e conciso
- Non inventare informazioni non presenti nel testo

Esempio formato risposta:
{{
    "abstract": "Documento di valutazione dei rischi...",
    "document_type_detailed": "DVR",
    "client_company": "ACME S.r.l.",
    "roles_list": [{{"role": "RSPP", "name": "Mario Rossi"}}],
    "risk_categories": ["Rischi meccanici", "Rischi chimici"],
    "departments": ["Produzione", "Amministrazione"],
    "key_topics": ["Sicurezza sul lavoro", "Prevenzione"],
    "compliance_standards": ["D.Lgs. 81/2008"],
    "document_status": "Approvato",
    "revision_info": "Rev. 10",
    "responsible_person": "Ing. Giuseppe Verdi",
    "approval_date": "2024-05-29",
    "validity_period": "1 anno",
    "related_documents": ["MOD0201 02 Premessa"]
}}
"""

            # Call the LLM
            response = self.llm.complete(prompt)
            response_text = response.text.strip()

            # Try to parse JSON response
            try:
                # Remove any markdown formatting if present
                if response_text.startswith('```json'):
                    response_text = response_text.replace('```json', '').replace('```', '').strip()
                elif response_text.startswith('```'):
                    response_text = response_text.replace('```', '').strip()

                attributes = json.loads(response_text)

                # Validate and clean the attributes
                cleaned_attributes = {}
                for key, value in attributes.items():
                    if value is not None and value != "" and value != []:
                        # Prefix with ai_ to distinguish from manual attributes
                        cleaned_attributes[f"ai_{key}"] = value

                logger.info("AI attribute extraction successful",
                           file_name=file_name,
                           attributes_extracted=len(cleaned_attributes))

                return cleaned_attributes

            except json.JSONDecodeError as e:
                logger.warning("Failed to parse AI response as JSON",
                              file_name=file_name,
                              response=response_text[:200],
                              error=str(e))
                return {}

        except Exception as e:
            logger.error("AI attribute extraction failed",
                        file_name=file_name,
                        error=str(e))
            return {}

    def _generate_document_id(self, file_path: Path, content: str) -> str:
        """Generate a unique document ID."""
        content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:8]
        file_hash = hashlib.md5(str(file_path).encode('utf-8')).hexdigest()[:8]
        return f"doc_{file_hash}_{content_hash}"
    
    def _extract_title_from_content(self, content: str) -> Optional[str]:
        """Extract title from content."""
        lines = content.strip().split('\n')
        if not lines:
            return None
        
        first_line = lines[0].strip()
        
        # Check for markdown header
        if first_line.startswith('#'):
            return first_line.lstrip('#').strip()
        
        # Check if first line looks like a title
        if len(first_line) < 100 and not first_line.endswith('.'):
            return first_line
        
        return None
    
    def _normalize_list_field(self, value: Any) -> List[str]:
        """Normalize a field to a list of strings."""
        if not value:
            return []
        
        if isinstance(value, str):
            if ',' in value:
                return [item.strip() for item in value.split(',') if item.strip()]
            else:
                return [value.strip()]
        elif isinstance(value, list):
            return [str(item).strip() for item in value if str(item).strip()]
        else:
            return [str(value).strip()]


class DocumentDeletionTool(BaseTool):
    """Tool for deleting documents from the collection."""

    def __init__(self):
        self.solr_url = f"{settings.solr_url}/{settings.solr_collection}"
        self.solr = pysolr.Solr(self.solr_url, always_commit=True)
        super().__init__()

    @property
    def metadata(self):
        """Return tool metadata."""
        return ToolMetadata(
            name="delete_document",
            description=(
                "Delete a document and all its chunks from the collection. "
                "Requires the document ID. Use with caution as this action cannot be undone."
            )
        )

    def __call__(self, *args, **kwargs) -> str:
        """Make the tool callable."""
        return self.call(*args, **kwargs)

    def call(self, document_id: str, **kwargs) -> str:
        """
        Delete a document by ID.
        
        Args:
            document_id: ID of the document to delete
            
        Returns:
            JSON string with deletion result
        """
        try:
            logger.info("Deleting document", document_id=document_id)
            
            # Check if document exists
            results = self.solr.search(f"document_id:{document_id}", rows=0)
            
            if results.hits == 0:
                return json.dumps({
                    "success": False,
                    "error": f"Document with ID {document_id} not found",
                    "document_id": document_id
                })
            
            # Delete document and all its chunks
            self.solr.delete(q=f"document_id:{document_id}")
            
            logger.info("Document deleted successfully", document_id=document_id)
            
            return json.dumps({
                "success": True,
                "message": f"Document {document_id} and all its chunks deleted successfully",
                "document_id": document_id,
                "documents_deleted": results.hits
            })
            
        except Exception as e:
            logger.error("Document deletion failed", document_id=document_id, error=str(e))
            return json.dumps({
                "success": False,
                "error": f"Deletion failed: {str(e)}",
                "document_id": document_id
            })


class DocumentDetailsTool(BaseTool):
    """Tool for getting detailed information about a document."""

    def __init__(self):
        self.solr_url = f"{settings.solr_url}/{settings.solr_collection}"
        self.solr = pysolr.Solr(self.solr_url, always_commit=True)
        super().__init__()

    @property
    def metadata(self):
        """Return tool metadata."""
        return ToolMetadata(
            name="get_document_details",
            description=(
                "Get detailed information about a specific document including "
                "metadata, content preview, and related chunks. Useful for "
                "understanding document structure and content."
            )
        )

    def __call__(self, *args, **kwargs) -> str:
        """Make the tool callable."""
        return self.call(*args, **kwargs)

    def call(self, document_id: str, include_content: bool = False, **kwargs) -> str:
        """
        Get document details.
        
        Args:
            document_id: ID of the document
            include_content: Whether to include full content
            
        Returns:
            JSON string with document details
        """
        try:
            logger.info("Getting document details", document_id=document_id)
            
            # Get main document
            doc_results = self.solr.search(
                f"document_id:{document_id} AND is_chunk:false", 
                rows=1,
                fl="*"
            )
            
            if not doc_results.docs:
                return json.dumps({
                    "error": f"Document with ID {document_id} not found",
                    "document_id": document_id
                })
            
            doc = doc_results.docs[0]
            
            # Get chunks if any
            chunk_results = self.solr.search(
                f"document_id:{document_id} AND is_chunk:true",
                rows=100,
                fl="id,chunk_index,content_length,word_count",
                sort="chunk_index asc"
            )
            
            # Prepare response
            response = {
                "document_id": document_id,
                "title": doc.get("title"),
                "document_type": doc.get("document_type"),
                "author": doc.get("author", []),
                "category": doc.get("category", []),
                "tags": doc.get("tags", []),
                "source_file": doc.get("source_file"),
                "file_path": doc.get("file_path"),
                "created_date": doc.get("created_date"),
                "modified_date": doc.get("modified_date"),
                "indexed_date": doc.get("indexed_date"),
                "content_length": doc.get("content_length", 0),
                "word_count": doc.get("word_count", 0),
                "language": doc.get("language"),
                "chunks": [
                    {
                        "chunk_id": chunk.get("id"),
                        "chunk_index": chunk.get("chunk_index"),
                        "content_length": chunk.get("content_length", 0),
                        "word_count": chunk.get("word_count", 0)
                    }
                    for chunk in chunk_results.docs
                ],
                "total_chunks": len(chunk_results.docs)
            }
            
            if include_content:
                response["content"] = doc.get("content", "")
            else:
                content = doc.get("content", "")
                response["content_preview"] = content[:500] + "..." if len(content) > 500 else content
            
            # Add custom fields
            custom_fields = {}
            for key, value in doc.items():
                if key.startswith("custom_"):
                    custom_fields[key[7:]] = value  # Remove "custom_" prefix
            
            if custom_fields:
                response["custom_fields"] = custom_fields
            
            logger.info("Document details retrieved", 
                       document_id=document_id,
                       chunks_found=len(chunk_results.docs))
            
            return json.dumps(response, indent=2)
            
        except Exception as e:
            logger.error("Get document details failed", 
                        document_id=document_id, error=str(e))
            return json.dumps({
                "error": f"Failed to get document details: {str(e)}",
                "document_id": document_id
            })
