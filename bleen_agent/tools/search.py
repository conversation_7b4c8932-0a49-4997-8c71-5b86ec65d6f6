"""
Document search tools for Bleen Agent.

Implements various search strategies using Solr without embeddings:
- Full-text search with relevance scoring
- Filtered search by metadata
- Faceted search for exploration
- Similar document finding based on content overlap
"""

from typing import Dict, List, Optional, Any, Union
import json
from datetime import datetime

import pysolr
from llama_index.core.tools import BaseTool
from llama_index.core.tools.types import ToolMetadata
from pydantic import BaseModel, Field

from ..core.config import settings
from ..utils.logging import get_logger

logger = get_logger(__name__)


class SearchResult(BaseModel):
    """Model for search results."""
    id: str
    title: Optional[str] = None
    content: str
    score: float
    document_type: Optional[str] = None
    author: Optional[List[str]] = None
    category: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    source_file: Optional[str] = None
    created_date: Optional[str] = None
    is_chunk: bool = False
    chunk_index: Optional[int] = None


class SolrConnection:
    """Manages connection to Solr."""
    
    def __init__(self):
        self.solr_url = f"{settings.solr_url}/{settings.solr_collection}"
        self.solr = pysolr.Solr(self.solr_url, always_commit=True)
        
    def ping(self) -> bool:
        """Test Solr connection."""
        try:
            self.solr.ping()
            return True
        except Exception as e:
            logger.error("Solr ping failed", error=str(e))
            return False


class DocumentSearchTool(BaseTool):
    """
    Tool for searching documents using full-text search.

    This tool provides comprehensive document search capabilities:
    - Keyword-based search with relevance ranking
    - Boolean operators (AND, OR, NOT)
    - Phrase searching with quotes
    - Field-specific searches
    """

    def __init__(self):
        self.solr_conn = SolrConnection()
        super().__init__()

    @property
    def metadata(self):
        """Return tool metadata."""
        return ToolMetadata(
            name="document_search",
            description=(
                "Search for documents using keywords, phrases, or boolean queries. "
                "Supports advanced search syntax including quotes for phrases, "
                "AND/OR/NOT operators, and field-specific searches like title:keyword. "
                "Returns relevant documents with scores and metadata."
            )
        )

    def __call__(self, *args, **kwargs) -> str:
        """Make the tool callable."""
        return self.call(*args, **kwargs)

    def call(self, query: str, max_results: int = 10, **kwargs) -> str:
        """
        Search documents using full-text search.
        
        Args:
            query: Search query (supports boolean operators, phrases, field searches)
            max_results: Maximum number of results to return
            
        Returns:
            JSON string with search results
        """
        try:
            logger.info("Executing document search", query=query, max_results=max_results)
            
            if not self.solr_conn.ping():
                return json.dumps({
                    "error": "Cannot connect to document search service",
                    "results": []
                })
            
            # Build Solr query
            solr_query = self._build_search_query(query)
            
            # Execute search
            results = self.solr_conn.solr.search(
                solr_query,
                rows=max_results,
                fl="*,score",
                sort="score desc",
                hl="true",
                hl_fl="content,title",
                hl_simple_pre="<mark>",
                hl_simple_post="</mark>",
                hl_fragsize=200
            )
            
            # Process results
            search_results = []
            for doc in results.docs:
                result = SearchResult(
                    id=doc.get("id", ""),
                    title=doc.get("title"),
                    content=self._get_highlighted_content(doc, results.highlighting),
                    score=doc.get("score", 0.0),
                    document_type=doc.get("document_type"),
                    author=doc.get("author", []),
                    category=doc.get("category", []),
                    tags=doc.get("tags", []),
                    source_file=doc.get("source_file"),
                    created_date=doc.get("created_date"),
                    is_chunk=doc.get("is_chunk", False),
                    chunk_index=doc.get("chunk_index")
                )
                search_results.append(result.dict())
            
            response = {
                "query": query,
                "total_found": results.hits,
                "results_returned": len(search_results),
                "results": search_results,
                "search_time_ms": getattr(results, 'qtime', 0)
            }
            
            logger.info("Document search completed", 
                       total_found=results.hits,
                       results_returned=len(search_results))
            
            return json.dumps(response, indent=2)
            
        except Exception as e:
            logger.error("Document search failed", query=query, error=str(e))
            return json.dumps({
                "error": f"Search failed: {str(e)}",
                "query": query,
                "results": []
            })
    
    def _build_search_query(self, query: str) -> str:
        """Build Solr query from user input."""
        # If query contains field syntax (field:value), use as-is
        if ":" in query and not query.startswith("http"):
            return query
        
        # For simple queries, search in content and title with boosting
        escaped_query = pysolr.Solr._from_python(query)
        return f"content:({escaped_query}) OR title:({escaped_query})^2"
    
    def _get_highlighted_content(self, doc: Dict, highlighting: Dict) -> str:
        """Get highlighted content or fallback to original."""
        doc_id = doc.get("id", "")
        
        if highlighting and doc_id in highlighting:
            highlights = highlighting[doc_id]
            if "content" in highlights and highlights["content"]:
                return " ... ".join(highlights["content"])
            elif "title" in highlights and highlights["title"]:
                return " ... ".join(highlights["title"])
        
        # Fallback to original content (truncated)
        content = doc.get("content", "")
        if len(content) > 500:
            return content[:500] + "..."
        return content


class FilteredSearchTool(BaseTool):
    """
    Tool for searching documents with metadata filters.

    Allows filtering by:
    - Document type (markdown, text, pdf, etc.)
    - Author
    - Category
    - Tags
    - Date ranges
    """

    def __init__(self):
        self.solr_conn = SolrConnection()
        super().__init__()

    @property
    def metadata(self):
        """Return tool metadata."""
        return ToolMetadata(
            name="filtered_search",
            description=(
                "Search documents with specific filters like author, category, tags, "
                "document type, or date ranges. Use this when you need to find documents "
                "matching specific criteria. Filters can be combined."
            )
        )

    def __call__(self, *args, **kwargs) -> str:
        """Make the tool callable."""
        return self.call(*args, **kwargs)

    def call(
        self, 
        query: str = "*:*",
        author: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[str] = None,
        document_type: Optional[str] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None,
        max_results: int = 10,
        **kwargs
    ) -> str:
        """
        Search documents with filters.
        
        Args:
            query: Text query (optional, defaults to all documents)
            author: Filter by author name
            category: Filter by category
            tags: Filter by tags
            document_type: Filter by document type
            date_from: Filter by date from (YYYY-MM-DD)
            date_to: Filter by date to (YYYY-MM-DD)
            max_results: Maximum results to return
            
        Returns:
            JSON string with filtered search results
        """
        try:
            logger.info("Executing filtered search", 
                       query=query, author=author, category=category, 
                       tags=tags, document_type=document_type)
            
            if not self.solr_conn.ping():
                return json.dumps({
                    "error": "Cannot connect to document search service",
                    "results": []
                })
            
            # Build filter queries
            filter_queries = []
            
            if author:
                filter_queries.append(f"author:{pysolr.Solr._from_python(author)}")
            
            if category:
                filter_queries.append(f"category:{pysolr.Solr._from_python(category)}")
            
            if tags:
                filter_queries.append(f"tags:{pysolr.Solr._from_python(tags)}")
            
            if document_type:
                filter_queries.append(f"document_type:{document_type}")
            
            if date_from or date_to:
                date_range = self._build_date_range(date_from, date_to)
                if date_range:
                    filter_queries.append(f"created_date:{date_range}")
            
            # Execute search
            search_params = {
                "rows": max_results,
                "fl": "*,score",
                "sort": "score desc"
            }
            
            if filter_queries:
                search_params["fq"] = filter_queries
            
            results = self.solr_conn.solr.search(query, **search_params)
            
            # Process results
            search_results = []
            for doc in results.docs:
                result = SearchResult(
                    id=doc.get("id", ""),
                    title=doc.get("title"),
                    content=doc.get("content", "")[:500] + "..." if len(doc.get("content", "")) > 500 else doc.get("content", ""),
                    score=doc.get("score", 0.0),
                    document_type=doc.get("document_type"),
                    author=doc.get("author", []),
                    category=doc.get("category", []),
                    tags=doc.get("tags", []),
                    source_file=doc.get("source_file"),
                    created_date=doc.get("created_date"),
                    is_chunk=doc.get("is_chunk", False),
                    chunk_index=doc.get("chunk_index")
                )
                search_results.append(result.dict())
            
            response = {
                "query": query,
                "filters_applied": {
                    "author": author,
                    "category": category,
                    "tags": tags,
                    "document_type": document_type,
                    "date_from": date_from,
                    "date_to": date_to
                },
                "total_found": results.hits,
                "results_returned": len(search_results),
                "results": search_results
            }
            
            logger.info("Filtered search completed", 
                       total_found=results.hits,
                       filters_count=len(filter_queries))
            
            return json.dumps(response, indent=2)
            
        except Exception as e:
            logger.error("Filtered search failed", error=str(e))
            return json.dumps({
                "error": f"Filtered search failed: {str(e)}",
                "results": []
            })
    
    def _build_date_range(self, date_from: Optional[str], date_to: Optional[str]) -> Optional[str]:
        """Build Solr date range query."""
        if not date_from and not date_to:
            return None
        
        start = f"{date_from}T00:00:00Z" if date_from else "*"
        end = f"{date_to}T23:59:59Z" if date_to else "*"
        
        return f"[{start} TO {end}]"


class SimilarDocumentsTool(BaseTool):
    """
    Tool for finding documents similar to a given document.

    Uses content-based similarity without embeddings:
    - Term frequency analysis
    - Common keywords extraction
    - Category and tag matching
    """

    def __init__(self):
        self.solr_conn = SolrConnection()
        super().__init__()

    @property
    def metadata(self):
        """Return tool metadata."""
        return ToolMetadata(
            name="find_similar_documents",
            description=(
                "Find documents similar to a given document ID. "
                "Uses content analysis, keywords, categories, and tags to find related documents. "
                "Useful for discovering related content or following up on topics."
            )
        )

    def __call__(self, *args, **kwargs) -> str:
        """Make the tool callable."""
        return self.call(*args, **kwargs)

    def call(self, document_id: str, max_results: int = 5, **kwargs) -> str:
        """
        Find documents similar to the given document.
        
        Args:
            document_id: ID of the reference document
            max_results: Maximum number of similar documents to return
            
        Returns:
            JSON string with similar documents
        """
        try:
            logger.info("Finding similar documents", document_id=document_id)
            
            if not self.solr_conn.ping():
                return json.dumps({
                    "error": "Cannot connect to document search service",
                    "results": []
                })
            
            # Get the reference document
            ref_doc_results = self.solr_conn.solr.search(f"id:{document_id}", rows=1)
            
            if not ref_doc_results.docs:
                return json.dumps({
                    "error": f"Document with ID {document_id} not found",
                    "results": []
                })
            
            ref_doc = ref_doc_results.docs[0]
            
            # Use Solr's More Like This functionality
            mlt_params = {
                "mlt": "true",
                "mlt.fl": "content,title",
                "mlt.mindf": 1,
                "mlt.mintf": 1,
                "mlt.maxqt": 25,
                "mlt.maxntp": 5000,
                "rows": max_results,
                "fl": "*,score"
            }
            
            # Execute More Like This query
            results = self.solr_conn.solr.search(f"id:{document_id}", **mlt_params)
            
            # Process results
            similar_docs = []
            if hasattr(results, 'moreLikeThis') and document_id in results.moreLikeThis:
                for doc in results.moreLikeThis[document_id]:
                    result = SearchResult(
                        id=doc.get("id", ""),
                        title=doc.get("title"),
                        content=doc.get("content", "")[:300] + "..." if len(doc.get("content", "")) > 300 else doc.get("content", ""),
                        score=doc.get("score", 0.0),
                        document_type=doc.get("document_type"),
                        author=doc.get("author", []),
                        category=doc.get("category", []),
                        tags=doc.get("tags", []),
                        source_file=doc.get("source_file"),
                        created_date=doc.get("created_date"),
                        is_chunk=doc.get("is_chunk", False),
                        chunk_index=doc.get("chunk_index")
                    )
                    similar_docs.append(result.dict())
            
            response = {
                "reference_document": {
                    "id": ref_doc.get("id"),
                    "title": ref_doc.get("title"),
                    "document_type": ref_doc.get("document_type")
                },
                "similar_documents": similar_docs,
                "total_found": len(similar_docs)
            }
            
            logger.info("Similar documents search completed", 
                       reference_id=document_id,
                       similar_found=len(similar_docs))
            
            return json.dumps(response, indent=2)
            
        except Exception as e:
            logger.error("Similar documents search failed", 
                        document_id=document_id, error=str(e))
            return json.dumps({
                "error": f"Similar documents search failed: {str(e)}",
                "reference_document_id": document_id,
                "results": []
            })
