"""
Document search tools for Bleen Agent.

Implements various search strategies using Solr without embeddings:
- Full-text search with relevance scoring
- Filtered search by metadata
- Faceted search for exploration
- Similar document finding based on content overlap
"""

from typing import Dict, List, Optional, Any, Union
import json
import re
from datetime import datetime

import pysolr
import markdown
from bs4 import BeautifulSoup
from llama_index.core.tools import BaseTool
from llama_index.core.tools.types import ToolMetadata
from pydantic import BaseModel

from ..core.config import settings
from ..utils.logging import get_logger

logger = get_logger(__name__)


class SearchResult(BaseModel):
    """Model for search results."""
    id: str
    title: Optional[str] = None
    content: str
    score: float
    document_type: Optional[str] = None
    author: Optional[List[str]] = None
    category: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    source_file: Optional[str] = None
    created_date: Optional[str] = None
    is_chunk: bool = False
    chunk_index: Optional[int] = None


class SolrConnection:
    """Manages connection to Solr."""

    def __init__(self):
        self.solr_url = f"{settings.solr_url}/{settings.solr_collection}"
        self.solr = pysolr.Solr(self.solr_url, always_commit=True)

    def ping(self) -> bool:
        """Test Solr connection."""
        try:
            # Try a simple search instead of ping
            result = self.solr.search("*:*", rows=0)
            return True
        except Exception as e:
            logger.error("Solr ping failed", error=str(e))
            return False


class MarkdownAnalyzer:
    """Analyzes Markdown content to extract structured information."""

    def __init__(self):
        self.md = markdown.Markdown(extensions=['tables', 'fenced_code'])

    def analyze_content(self, content: str) -> Dict[str, Any]:
        """
        Analyze Markdown content and extract structured information.

        Args:
            content: Raw Markdown content

        Returns:
            Dictionary with analyzed content including tables, headers, etc.
        """
        try:
            # Convert Markdown to HTML
            html = self.md.convert(content)
            soup = BeautifulSoup(html, 'html.parser')

            analysis = {
                "original_content": content,
                "html_content": html,
                "tables": self._extract_tables(soup),
                "headers": self._extract_headers(soup),
                "lists": self._extract_lists(soup),
                "code_blocks": self._extract_code_blocks(content),
                "structured_content": self._create_structured_content(soup)
            }

            return analysis

        except Exception as e:
            logger.error("Markdown analysis failed", error=str(e))
            return {
                "original_content": content,
                "error": str(e),
                "tables": [],
                "headers": [],
                "lists": [],
                "code_blocks": [],
                "structured_content": content
            }

    def _extract_tables(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract and analyze tables with row-column associations."""
        tables = []

        for table_elem in soup.find_all('table'):
            table_data = {
                "headers": [],
                "rows": [],
                "cell_associations": {}
            }

            # Extract headers
            header_row = table_elem.find('thead')
            if header_row:
                headers = header_row.find_all(['th', 'td'])
                table_data["headers"] = [h.get_text(strip=True) for h in headers]

            # Extract rows
            tbody = table_elem.find('tbody') or table_elem
            rows = tbody.find_all('tr')

            for row_idx, row in enumerate(rows):
                cells = row.find_all(['td', 'th'])
                row_data = []

                for col_idx, cell in enumerate(cells):
                    cell_text = cell.get_text(strip=True)
                    row_data.append(cell_text)

                    # Create cell association
                    header_name = table_data["headers"][col_idx] if col_idx < len(table_data["headers"]) else f"Column_{col_idx}"

                    # Store cell association with context
                    cell_key = f"row_{row_idx}_col_{col_idx}"
                    table_data["cell_associations"][cell_key] = {
                        "value": cell_text,
                        "row_index": row_idx,
                        "column_index": col_idx,
                        "header": header_name,
                        "context": f"In table row {row_idx + 1}, column '{header_name}': {cell_text}"
                    }

                table_data["rows"].append(row_data)

            tables.append(table_data)

        return tables

    def _extract_headers(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extract headers with hierarchy."""
        headers = []

        for i in range(1, 7):  # h1 to h6
            for header in soup.find_all(f'h{i}'):
                headers.append({
                    "level": i,
                    "text": header.get_text(strip=True),
                    "tag": f"h{i}"
                })

        return headers

    def _extract_lists(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract lists (ordered and unordered)."""
        lists = []

        for list_elem in soup.find_all(['ul', 'ol']):
            list_data = {
                "type": list_elem.name,
                "items": []
            }

            for item in list_elem.find_all('li', recursive=False):
                list_data["items"].append(item.get_text(strip=True))

            lists.append(list_data)

        return lists

    def _extract_code_blocks(self, content: str) -> List[Dict[str, str]]:
        """Extract code blocks from Markdown."""
        code_blocks = []

        # Find fenced code blocks
        fenced_pattern = r'```(\w+)?\n(.*?)\n```'
        for match in re.finditer(fenced_pattern, content, re.DOTALL):
            language = match.group(1) or "text"
            code = match.group(2)
            code_blocks.append({
                "language": language,
                "code": code.strip()
            })

        # Find inline code
        inline_pattern = r'`([^`]+)`'
        for match in re.finditer(inline_pattern, content):
            code_blocks.append({
                "language": "inline",
                "code": match.group(1)
            })

        return code_blocks

    def _create_structured_content(self, soup: BeautifulSoup) -> str:
        """Create a structured text representation with enhanced context."""
        structured_parts = []

        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'table', 'ul', 'ol']):
            if element.name.startswith('h'):
                level = element.name[1]
                structured_parts.append(f"\n{'#' * int(level)} {element.get_text(strip=True)}\n")

            elif element.name == 'table':
                structured_parts.append(self._format_table_for_text(element))

            elif element.name in ['ul', 'ol']:
                list_text = "\n".join([f"- {li.get_text(strip=True)}" for li in element.find_all('li', recursive=False)])
                structured_parts.append(f"\n{list_text}\n")

            elif element.name == 'p':
                structured_parts.append(f"{element.get_text(strip=True)}\n")

        return "\n".join(structured_parts)

    def _format_table_for_text(self, table_elem) -> str:
        """Format table as readable text with clear row-column associations."""
        lines = ["\n[TABLE]"]

        # Headers
        header_row = table_elem.find('thead')
        if header_row:
            headers = [h.get_text(strip=True) for h in header_row.find_all(['th', 'td'])]
            lines.append("Headers: " + " | ".join(headers))
            lines.append("-" * 50)

        # Rows with explicit associations
        tbody = table_elem.find('tbody') or table_elem
        rows = tbody.find_all('tr')

        for row_idx, row in enumerate(rows):
            cells = [cell.get_text(strip=True) for cell in row.find_all(['td', 'th'])]

            # Create readable row representation
            if header_row and len(headers) == len(cells):
                row_associations = []
                for header, cell in zip(headers, cells):
                    if cell.strip():  # Only include non-empty cells
                        row_associations.append(f"{header}: {cell}")
                lines.append(f"Row {row_idx + 1}: " + " | ".join(row_associations))
            else:
                lines.append(f"Row {row_idx + 1}: " + " | ".join(cells))

        lines.append("[/TABLE]\n")
        return "\n".join(lines)


class DocumentSearchTool(BaseTool):
    """
    Tool for searching documents using full-text search.

    This tool provides comprehensive document search capabilities:
    - Keyword-based search with relevance ranking
    - Boolean operators (AND, OR, NOT)
    - Phrase searching with quotes
    - Field-specific searches
    - Markdown analysis with table interpretation
    """

    def __init__(self):
        self.solr_conn = SolrConnection()
        self.markdown_analyzer = MarkdownAnalyzer()
        super().__init__()

    @property
    def metadata(self):
        """Return tool metadata."""
        return ToolMetadata(
            name="document_search",
            description=(
                "Search for documents using keywords, phrases, or boolean queries. "
                "Supports advanced search syntax including quotes for phrases, "
                "AND/OR/NOT operators, and field-specific searches like title:keyword. "
                "Returns relevant documents with scores and metadata. "
                "Use 'input' parameter for the search query."
            )
        )

    def __call__(self, *args, **kwargs) -> str:
        """Make the tool callable."""
        return self.call(*args, **kwargs)

    def call(self, query: str = None, input: str = None, max_results: int = 10, **kwargs) -> str:
        """
        Search documents using full-text search.

        Args:
            query: Search query (supports boolean operators, phrases, field searches)
            input: Alternative parameter name for search query (for compatibility)
            max_results: Maximum number of results to return

        Returns:
            JSON string with search results
        """
        # Handle both 'query' and 'input' parameter names
        search_query = query or input
        if not search_query:
            return json.dumps({
                "error": "No search query provided. Use 'query' or 'input' parameter.",
                "results": []
            })
        try:
            logger.info("Executing document search", query=search_query, max_results=max_results)
            
            if not self.solr_conn.ping():
                return json.dumps({
                    "error": "Cannot connect to document search service",
                    "results": []
                })
            
            # Build Solr query
            solr_query = self._build_search_query(search_query)
            
            # Execute search
            results = self.solr_conn.solr.search(
                solr_query,
                rows=max_results,
                fl="*,score",
                sort="score desc",
                hl="true",
                hl_fl="content,title",
                hl_simple_pre="<mark>",
                hl_simple_post="</mark>",
                hl_fragsize=200
            )
            
            # Process results
            search_results = []
            for doc in results.docs:
                # Handle content field that might be a list or string
                content = self._get_highlighted_content(doc, results.highlighting)
                if isinstance(content, list):
                    content = " ".join(str(item) for item in content)
                elif content is None:
                    content = ""
                else:
                    content = str(content)

                result = SearchResult(
                    id=doc.get("id", ""),
                    title=doc.get("title"),
                    content=content,
                    score=doc.get("score", 0.0),
                    document_type=doc.get("document_type"),
                    author=doc.get("author", []),
                    category=doc.get("category", []),
                    tags=doc.get("tags", []),
                    source_file=doc.get("source_file"),
                    created_date=doc.get("created_date"),
                    is_chunk=doc.get("is_chunk", False),
                    chunk_index=doc.get("chunk_index")
                )
                search_results.append(result.dict())
            
            response = {
                "query": search_query,
                "total_found": results.hits,
                "results_returned": len(search_results),
                "results": search_results,
                "search_time_ms": getattr(results, 'qtime', 0)
            }
            
            logger.info("Document search completed",
                       total_found=results.hits,
                       results_returned=len(search_results))

            # Create a response object with raw_output attribute for OpenAI Agent compatibility
            class ToolResponse:
                def __init__(self, data):
                    self.raw_output = json.dumps(data, indent=2)
                    self.data = data

                def __str__(self):
                    return self.raw_output

            return ToolResponse(response)
            
        except Exception as e:
            logger.error("Document search failed", query=search_query, error=str(e))
            error_response = {
                "error": f"Search failed: {str(e)}",
                "query": search_query,
                "results": []
            }

            # Create a response object with raw_output attribute for OpenAI Agent compatibility
            class ToolResponse:
                def __init__(self, data):
                    self.raw_output = json.dumps(data, indent=2)
                    self.data = data

                def __str__(self):
                    return self.raw_output

            return ToolResponse(error_response)
    
    def _build_search_query(self, query: str) -> str:
        """Build Solr query from user input."""
        # If query contains field syntax (field:value), use as-is
        if ":" in query and not query.startswith("http"):
            return query

        # For simple queries, search in content and title with boosting
        # Use simple escaping instead of pysolr._from_python
        escaped_query = query.replace('"', '\\"').replace('\\', '\\\\')
        return f'content:("{escaped_query}") OR title:("{escaped_query}")^2'
    
    def _get_highlighted_content(self, doc: Dict, highlighting: Dict) -> str:
        """Get highlighted content with Markdown analysis."""
        doc_id = doc.get("id", "")
        content = ""

        # First try to get highlighted content
        if highlighting and doc_id in highlighting:
            highlights = highlighting[doc_id]
            if "content" in highlights and highlights["content"]:
                content = " ... ".join(highlights["content"])
            elif "title" in highlights and highlights["title"]:
                content = " ... ".join(highlights["title"])

        # Fallback to original content
        if not content:
            content = doc.get("content", "")

        # Handle content that might be a list
        if isinstance(content, list):
            content = " ".join(str(item) for item in content)
        elif content is None:
            content = ""
        else:
            content = str(content)

        # Analyze content as Markdown if it contains Markdown syntax
        if self._is_markdown_content(content):
            try:
                analysis = self.markdown_analyzer.analyze_content(content)

                # Create enhanced content with structured information
                enhanced_content = []

                # Add structured content
                if analysis.get("structured_content"):
                    enhanced_content.append("=== STRUCTURED CONTENT ===")
                    enhanced_content.append(analysis["structured_content"][:1000])

                # Add table information if present
                if analysis.get("tables"):
                    enhanced_content.append("\n=== TABLES FOUND ===")
                    for i, table in enumerate(analysis["tables"]):
                        enhanced_content.append(f"\nTable {i+1}:")
                        if table.get("headers"):
                            enhanced_content.append(f"Headers: {', '.join(table['headers'])}")

                        # Add sample cell associations
                        if table.get("cell_associations"):
                            enhanced_content.append("Sample data:")
                            for j, (key, cell_info) in enumerate(table["cell_associations"].items()):
                                if j < 3:  # Show first 3 cells as examples
                                    enhanced_content.append(f"  - {cell_info['context']}")

                # Add headers if present
                if analysis.get("headers"):
                    enhanced_content.append("\n=== DOCUMENT STRUCTURE ===")
                    for header in analysis["headers"][:5]:  # Show first 5 headers
                        enhanced_content.append(f"{'#' * header['level']} {header['text']}")

                result = "\n".join(enhanced_content)

                # Truncate if too long
                if len(result) > 1500:
                    return result[:1500] + "\n... [Content truncated - Markdown analysis applied]"
                return result

            except Exception as e:
                logger.warning("Markdown analysis failed, using original content", error=str(e))

        # Standard truncation for non-Markdown content
        if len(content) > 500:
            return content[:500] + "..."
        return content

    def _is_markdown_content(self, content: str) -> bool:
        """Check if content appears to be Markdown."""
        markdown_indicators = [
            r'^\s*#',  # Headers
            r'\|.*\|',  # Tables
            r'^\s*[-*+]\s',  # Lists
            r'```',  # Code blocks
            r'\[.*\]\(.*\)',  # Links
            r'\*\*.*\*\*',  # Bold
            r'_.*_'  # Italic
        ]

        for pattern in markdown_indicators:
            if re.search(pattern, content, re.MULTILINE):
                return True
        return False


class FilteredSearchTool(BaseTool):
    """
    Tool for searching documents with metadata filters.

    Allows filtering by:
    - Document type (markdown, text, pdf, etc.)
    - Author
    - Category
    - Tags
    - Date ranges
    """

    def __init__(self):
        self.solr_conn = SolrConnection()
        super().__init__()

    @property
    def metadata(self):
        """Return tool metadata."""
        return ToolMetadata(
            name="filtered_search",
            description=(
                "Search documents with specific filters like author, category, tags, "
                "document type, or date ranges. Use this when you need to find documents "
                "matching specific criteria. Filters can be combined."
            )
        )

    def __call__(self, *args, **kwargs) -> str:
        """Make the tool callable."""
        return self.call(*args, **kwargs)

    def call(
        self, 
        query: str = "*:*",
        author: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[str] = None,
        document_type: Optional[str] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None,
        max_results: int = 10,
        **kwargs
    ) -> str:
        """
        Search documents with filters.
        
        Args:
            query: Text query (optional, defaults to all documents)
            author: Filter by author name
            category: Filter by category
            tags: Filter by tags
            document_type: Filter by document type
            date_from: Filter by date from (YYYY-MM-DD)
            date_to: Filter by date to (YYYY-MM-DD)
            max_results: Maximum results to return
            
        Returns:
            JSON string with filtered search results
        """
        try:
            logger.info("Executing filtered search", 
                       query=query, author=author, category=category, 
                       tags=tags, document_type=document_type)
            
            if not self.solr_conn.ping():
                return json.dumps({
                    "error": "Cannot connect to document search service",
                    "results": []
                })
            
            # Build filter queries
            filter_queries = []
            
            if author:
                escaped_author = author.replace('"', '\\"').replace('\\', '\\\\')
                filter_queries.append(f'author:"{escaped_author}"')

            if category:
                escaped_category = category.replace('"', '\\"').replace('\\', '\\\\')
                filter_queries.append(f'category:"{escaped_category}"')

            if tags:
                escaped_tags = tags.replace('"', '\\"').replace('\\', '\\\\')
                filter_queries.append(f'tags:"{escaped_tags}"')
            
            if document_type:
                filter_queries.append(f"document_type:{document_type}")
            
            if date_from or date_to:
                date_range = self._build_date_range(date_from, date_to)
                if date_range:
                    filter_queries.append(f"created_date:{date_range}")
            
            # Execute search
            search_params = {
                "rows": max_results,
                "fl": "*,score",
                "sort": "score desc"
            }
            
            if filter_queries:
                search_params["fq"] = filter_queries
            
            results = self.solr_conn.solr.search(query, **search_params)
            
            # Process results
            search_results = []
            for doc in results.docs:
                result = SearchResult(
                    id=doc.get("id", ""),
                    title=doc.get("title"),
                    content=doc.get("content", "")[:500] + "..." if len(doc.get("content", "")) > 500 else doc.get("content", ""),
                    score=doc.get("score", 0.0),
                    document_type=doc.get("document_type"),
                    author=doc.get("author", []),
                    category=doc.get("category", []),
                    tags=doc.get("tags", []),
                    source_file=doc.get("source_file"),
                    created_date=doc.get("created_date"),
                    is_chunk=doc.get("is_chunk", False),
                    chunk_index=doc.get("chunk_index")
                )
                search_results.append(result.dict())
            
            response = {
                "query": query,
                "filters_applied": {
                    "author": author,
                    "category": category,
                    "tags": tags,
                    "document_type": document_type,
                    "date_from": date_from,
                    "date_to": date_to
                },
                "total_found": results.hits,
                "results_returned": len(search_results),
                "results": search_results
            }
            
            logger.info("Filtered search completed", 
                       total_found=results.hits,
                       filters_count=len(filter_queries))
            
            return json.dumps(response, indent=2)
            
        except Exception as e:
            logger.error("Filtered search failed", error=str(e))
            return json.dumps({
                "error": f"Filtered search failed: {str(e)}",
                "results": []
            })
    
    def _build_date_range(self, date_from: Optional[str], date_to: Optional[str]) -> Optional[str]:
        """Build Solr date range query."""
        if not date_from and not date_to:
            return None
        
        start = f"{date_from}T00:00:00Z" if date_from else "*"
        end = f"{date_to}T23:59:59Z" if date_to else "*"
        
        return f"[{start} TO {end}]"


class SimilarDocumentsTool(BaseTool):
    """
    Tool for finding documents similar to a given document.

    Uses content-based similarity without embeddings:
    - Term frequency analysis
    - Common keywords extraction
    - Category and tag matching
    """

    def __init__(self):
        self.solr_conn = SolrConnection()
        super().__init__()

    @property
    def metadata(self):
        """Return tool metadata."""
        return ToolMetadata(
            name="find_similar_documents",
            description=(
                "Find documents similar to a given document ID. "
                "Uses content analysis, keywords, categories, and tags to find related documents. "
                "Useful for discovering related content or following up on topics."
            )
        )

    def __call__(self, *args, **kwargs) -> str:
        """Make the tool callable."""
        return self.call(*args, **kwargs)

    def call(self, document_id: str, max_results: int = 5, **kwargs) -> str:
        """
        Find documents similar to the given document.
        
        Args:
            document_id: ID of the reference document
            max_results: Maximum number of similar documents to return
            
        Returns:
            JSON string with similar documents
        """
        try:
            logger.info("Finding similar documents", document_id=document_id)
            
            if not self.solr_conn.ping():
                return json.dumps({
                    "error": "Cannot connect to document search service",
                    "results": []
                })
            
            # Get the reference document
            ref_doc_results = self.solr_conn.solr.search(f"id:{document_id}", rows=1)
            
            if not ref_doc_results.docs:
                return json.dumps({
                    "error": f"Document with ID {document_id} not found",
                    "results": []
                })
            
            ref_doc = ref_doc_results.docs[0]
            
            # Use Solr's More Like This functionality
            mlt_params = {
                "mlt": "true",
                "mlt.fl": "content,title",
                "mlt.mindf": 1,
                "mlt.mintf": 1,
                "mlt.maxqt": 25,
                "mlt.maxntp": 5000,
                "rows": max_results,
                "fl": "*,score"
            }
            
            # Execute More Like This query
            results = self.solr_conn.solr.search(f"id:{document_id}", **mlt_params)
            
            # Process results
            similar_docs = []
            if hasattr(results, 'moreLikeThis') and document_id in results.moreLikeThis:
                for doc in results.moreLikeThis[document_id]:
                    result = SearchResult(
                        id=doc.get("id", ""),
                        title=doc.get("title"),
                        content=doc.get("content", "")[:300] + "..." if len(doc.get("content", "")) > 300 else doc.get("content", ""),
                        score=doc.get("score", 0.0),
                        document_type=doc.get("document_type"),
                        author=doc.get("author", []),
                        category=doc.get("category", []),
                        tags=doc.get("tags", []),
                        source_file=doc.get("source_file"),
                        created_date=doc.get("created_date"),
                        is_chunk=doc.get("is_chunk", False),
                        chunk_index=doc.get("chunk_index")
                    )
                    similar_docs.append(result.dict())
            
            response = {
                "reference_document": {
                    "id": ref_doc.get("id"),
                    "title": ref_doc.get("title"),
                    "document_type": ref_doc.get("document_type")
                },
                "similar_documents": similar_docs,
                "total_found": len(similar_docs)
            }
            
            logger.info("Similar documents search completed", 
                       reference_id=document_id,
                       similar_found=len(similar_docs))
            
            return json.dumps(response, indent=2)
            
        except Exception as e:
            logger.error("Similar documents search failed", 
                        document_id=document_id, error=str(e))
            return json.dumps({
                "error": f"Similar documents search failed: {str(e)}",
                "reference_document_id": document_id,
                "results": []
            })
