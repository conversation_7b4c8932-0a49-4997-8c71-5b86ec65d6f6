#!/usr/bin/env python3
"""
Script per creare un documento DOCX di test per dimostrare l'import.
"""

from docx import Document
from docx.shared import Inches

def create_test_docx():
    """Crea un documento DOCX di test."""
    
    # Crea un nuovo documento
    doc = Document()
    
    # Imposta le proprietà del documento
    doc.core_properties.title = "Documento DOCX di Test"
    doc.core_properties.author = "<PERSON><PERSON>"
    doc.core_properties.subject = "Test Import"
    doc.core_properties.keywords = "test, docx, bleen-agent, import"
    doc.core_properties.comments = "Documento creato per testare l'import di file DOCX nel Bleen Agent"
    
    # Aggiungi il titolo
    title = doc.add_heading('Documento DOCX di Test per Bleen Agent', 0)
    
    # Aggiungi paragrafi
    doc.add_heading('Introduzione', level=1)
    p1 = doc.add_paragraph(
        'Questo è un documento Microsoft Word (.docx) creato per testare '
        'la funzionalità di import del Bleen Agent. Il documento contiene '
        'diversi tipi di contenuto per verificare che l\'estrazione del testo '
        'funzioni correttamente.'
    )
    
    doc.add_heading('Caratteristiche del Test', level=1)
    p2 = doc.add_paragraph('Il documento include:')
    
    # Lista puntata
    doc.add_paragraph('Titoli di diversi livelli', style='List Bullet')
    doc.add_paragraph('Paragrafi di testo normale', style='List Bullet')
    doc.add_paragraph('Liste puntate', style='List Bullet')
    doc.add_paragraph('Tabelle con dati', style='List Bullet')
    doc.add_paragraph('Metadati del documento', style='List Bullet')
    
    doc.add_heading('Informazioni Tecniche', level=1)
    
    # Lista numerata
    doc.add_paragraph('Sistema: Bleen Agent v0.2.0', style='List Number')
    doc.add_paragraph('Formato: Microsoft Word (.docx)', style='List Number')
    doc.add_paragraph('Libreria: python-docx', style='List Number')
    doc.add_paragraph('Scopo: Test funzionalità import', style='List Number')
    
    # Aggiungi una tabella
    doc.add_heading('Tabella di Test', level=2)
    table = doc.add_table(rows=1, cols=3)
    table.style = 'Table Grid'
    
    # Intestazioni della tabella
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Campo'
    hdr_cells[1].text = 'Valore'
    hdr_cells[2].text = 'Descrizione'
    
    # Aggiungi righe alla tabella
    test_data = [
        ('Autore', 'Mauro Pelizzari', 'Creatore del documento'),
        ('Data', '18 Giugno 2025', 'Data di creazione'),
        ('Formato', 'DOCX', 'Microsoft Word Document'),
        ('Dimensione', 'Variabile', 'Dipende dal contenuto'),
        ('Encoding', 'UTF-8', 'Codifica del testo')
    ]
    
    for campo, valore, descrizione in test_data:
        row_cells = table.add_row().cells
        row_cells[0].text = campo
        row_cells[1].text = valore
        row_cells[2].text = descrizione
    
    doc.add_heading('Conclusioni', level=1)
    conclusion = doc.add_paragraph(
        'Se questo documento viene importato correttamente nel Bleen Agent, '
        'dovrebbe essere possibile:'
    )
    
    doc.add_paragraph('Cercare per titolo: "Documento DOCX di Test"', style='List Bullet')
    doc.add_paragraph('Trovare per autore: "Mauro Pelizzari"', style='List Bullet')
    doc.add_paragraph('Filtrare per categoria: "Test Import"', style='List Bullet')
    doc.add_paragraph('Ricercare nel contenuto: "Bleen Agent"', style='List Bullet')
    doc.add_paragraph('Trovare dati della tabella: "UTF-8", "DOCX"', style='List Bullet')
    
    final_para = doc.add_paragraph(
        '\nQuesto completa il documento di test DOCX per verificare '
        'l\'import e l\'indicizzazione nel sistema Bleen Agent.'
    )
    
    # Salva il documento
    doc.save('test_import.docx')
    print("✅ Documento DOCX di test creato: test_import.docx")

if __name__ == "__main__":
    create_test_docx()
