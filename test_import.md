---
title: "Documento di Test per Import"
author: "<PERSON><PERSON>"
category: ["test", "demo"]
tags: ["import", "bleen-agent", "test"]
language: "it"
---

# Documento di Test per Import

Questo è un documento di test creato per dimostrare la funzionalità di import del Bleen Agent.

## Contenuto del Test

Il documento contiene:

- **Frontmatter YAML** con metadati strutturati
- **Titoli** di diversi livelli
- **Paragrafi** di testo normale
- **Liste** puntate e numerate
- **Formattazione** in **grassetto** e *corsivo*

### Sezione Tecnica

Questo documento serve per testare:

1. L'import di file Markdown con frontmatter
2. L'estrazione automatica dei metadati
3. L'indicizzazione del contenuto in Solr
4. La ricerca successiva del contenuto importato

### Informazioni Aggiuntive

- **Data di creazione**: 18 giugno 2025
- **Scopo**: Test funzionalità import
- **Sistema**: Bleen Agent v0.2.0
- **Tecnologie**: <PERSON>lamaI<PERSON>x, Apache Solr, Python

## Conclusione

Se questo documento viene importato correttamente, dovrebbe essere possibile:

- Cercarlo per titolo: "Documento di Test per Import"
- Trovarlo per autore: "Mauro Pelizzari"
- Filtrarlo per categoria: "test" o "demo"
- Ricercarlo per tag: "import", "bleen-agent", "test"
- Cercare nel contenuto: "funzionalità di import"

Questo completa il documento di test per l'import.
